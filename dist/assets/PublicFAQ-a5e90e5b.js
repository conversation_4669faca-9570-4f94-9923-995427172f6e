import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i}from"./vendor-851db8c1.js";import{M as q,T as C,aS as h,b3 as k,aP as F}from"./index-5b566256.js";import{m as y,A as S}from"./framer-motion-c6ba3e69.js";let A=new q,Q=new C;const P=()=>{const[l,b]=i.useState([]),[c,p]=i.useState([]),[j,d]=i.useState(!0),[m,N]=i.useState({}),[o,x]=i.useState("all");i.useEffect(()=>{w()},[]);const w=async()=>{d(!0);try{const t=await Q.getPaginate("faq",{page:1,limit:100,filter:["club_id,eq,0","general,eq,1"],join:["faq_subcategory|subcategory_id",`${A._project_id}_faq_category|faq_subcategory.category_id,id`]});if(t&&t.list){b(t.list);const s=t.list.reduce((a,r)=>{var g,f;const n=(f=(g=r.faq_subcategory)==null?void 0:g.faq_category)==null?void 0:f.name;return n&&!a.find(_=>_.name===n)&&a.push({id:r.faq_subcategory.faq_category.id,name:n}),a},[]);p(s)}}catch(t){console.error("Error fetching FAQs:",t)}finally{d(!1)}},v=t=>{N(s=>({...s,[t]:!s[t]}))},u=o==="all"?l:l.filter(t=>{var s,a;return((a=(s=t.faq_subcategory)==null?void 0:s.faq_category)==null?void 0:a.id)===o});return j?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#005954]"})}):e.jsx("section",{id:"faq",className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-4xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx(h,{className:"text-[#005954]"}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"FAQ"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Frequently Asked Questions"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Find answers to common questions about Court Matchup and how it can help your tennis club."})]}),c.length>1&&e.jsxs("div",{className:"mb-8 flex flex-wrap justify-center gap-2",children:[e.jsx("button",{onClick:()=>x("all"),className:`rounded-full px-4 py-2 text-sm font-medium transition-colors ${o==="all"?"bg-[#005954] text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"All Categories"}),c.map(t=>e.jsx("button",{onClick:()=>x(t.id),className:`rounded-full px-4 py-2 text-sm font-medium transition-colors ${o===t.id?"bg-[#005954] text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:t.name},t.id))]}),e.jsx("div",{className:"space-y-4",children:u.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(h,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No FAQs available at the moment."})]}):u.map((t,s)=>{var a,r,n;return e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:s*.1},className:"overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm",children:[e.jsxs("button",{onClick:()=>v(t.id),className:"flex w-full items-center justify-between p-4 text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#005954] focus:ring-inset",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-ubuntu text-lg font-medium text-[#004540]",children:((a=t.faq_subcategory)==null?void 0:a.name)||"General Question"}),((n=(r=t.faq_subcategory)==null?void 0:r.faq_category)==null?void 0:n.name)&&e.jsx("span",{className:"mt-1 inline-block rounded-full bg-[#e8f5f4] px-2 py-1 text-xs font-medium text-[#005954]",children:t.faq_subcategory.faq_category.name})]}),e.jsx("div",{className:"ml-4 flex-shrink-0",children:m[t.id]?e.jsx(k,{className:"h-5 w-5 text-[#005954]"}):e.jsx(F,{className:"h-5 w-5 text-[#005954]"})})]}),e.jsx(S,{children:m[t.id]&&e.jsx(y.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"overflow-hidden",children:e.jsx("div",{className:"border-t border-gray-200 p-4",children:e.jsx("div",{className:"font-inter text-sm text-gray-700 leading-relaxed",dangerouslySetInnerHTML:{__html:t.answer}})})})})]},t.id)})}),e.jsx("div",{className:"mt-12 text-center",children:e.jsxs("div",{className:"rounded-lg bg-[#f9f9f6] p-6",children:[e.jsx("h3",{className:"font-ubuntu text-lg font-medium text-[#004540] mb-2",children:"Still have questions?"}),e.jsx("p",{className:"font-inter text-sm text-gray-600 mb-4",children:"Can't find the answer you're looking for? Please get in touch with our support team."}),e.jsx("button",{onClick:()=>{const t=document.getElementById("contact");t&&t.scrollIntoView({behavior:"smooth"})},className:"font-ubuntu rounded-full bg-[#005954] px-6 py-3 text-white transition-all hover:bg-[#004a45]",children:"Contact Support"})]})})]})})},D=P;export{D as P};
