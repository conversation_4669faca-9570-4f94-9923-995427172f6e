import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as t,r as c,f as Te}from"./vendor-851db8c1.js";import{M as X,G as q,R as xe,e as me,X as R,Y,v as pe,I as De,d as $e,H as ge,E as ee,J as he,b as ie,a3 as ne,T as Ae,A as Fe,c as Oe,i as de,t as He,Z as W,_ as Ve}from"./index-5b566256.js";import{c as ze,a as U}from"./yup-54691517.js";import{u as Ie}from"./react-hook-form-687afde5.js";import{o as Ze}from"./yup-2824f222.js";import{P as Be}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Ge from"./Skeleton-1e8bf077.js";import{f as ce}from"./date-fns-07266b7d.js";import{L as Je}from"./LoadingOverlay-87926629.js";import{P as Ue,R as Ke,T as Ye,F as Xe}from"./ReservationStatus-5ced670f.js";import{D as Qe}from"./DataTable-a2248415.js";import{H as We}from"./HistoryComponent-4f475aec.js";new X;const Re=({isOpen:f,onClose:r,reservation:s,userList:o,sports:_,setReservation:D,getData:z,setShowCheckin:k,setShowCheckout:d,courts:j})=>{var l,v,P,L,i,S,A,w,E,F,I,O,u,y,H,V,Z,B,Q,G;if(!s)return null;t.useContext(q);const[b,N]=t.useState(!1);JSON.parse((l=s==null?void 0:s.booking)==null?void 0:l.player_ids);const $=T=>{const C=o.find(J=>J.id===T);return C?C.first_name+" "+C.last_name:"--"},M=()=>{s.check_in==0&&k(!0),s.check_in==1&&d(!0)},x=(v=j.find(T=>T.id==s.booking.court_id))==null?void 0:v.name;return e.jsxs(xe,{isOpen:f,onClose:r,title:"Reservation detail",showFooter:!1,children:[b&&e.jsx(me,{}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"mt-4",children:e.jsxs("button",{onClick:M,className:"inline-flex items-center gap-2 rounded-xl border px-3 py-1.5 text-sm",children:[(s==null?void 0:s.check_in)===1?"Check-out":"Check-in",e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Reserved by"}),e.jsxs("p",{className:"mt-1 font-medium capitalize",children:[(P=s==null?void 0:s.user)==null?void 0:P.first_name," ",(L=s==null?void 0:s.user)==null?void 0:L.last_name]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Date & Time"}),e.jsxs("p",{className:"mt-1 font-medium",children:[R((i=s==null?void 0:s.booking)==null?void 0:i.date)," •"," ",Y((S=s==null?void 0:s.booking)==null?void 0:S.start_time)," -"," ",Y((A=s==null?void 0:s.booking)==null?void 0:A.end_time)]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Reserved on"}),e.jsx("p",{className:"mt-1 font-medium",children:R((w=s==null?void 0:s.booking)==null?void 0:w.create_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Space assigned"}),e.jsx("p",{className:"mt-1 font-medium",children:x||"Not assigned"})]}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["Players (",(E=s==null?void 0:s.booking)!=null&&E.player_ids?JSON.parse((F=s==null?void 0:s.booking)==null?void 0:F.player_ids).length:0,")"]}),e.jsx("div",{className:"mt-1 space-y-1",children:((I=s==null?void 0:s.booking)==null?void 0:I.player_ids)&&JSON.parse((O=s==null?void 0:s.booking)==null?void 0:O.player_ids).map((T,C)=>e.jsx("p",{className:"font-medium capitalize",children:$(T)},C))})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Sport type"}),e.jsxs("p",{className:"mt-1 font-medium",children:[(u=_==null?void 0:_.find(T=>{var C;return T.id===((C=s==null?void 0:s.booking)==null?void 0:C.sport_id)}))==null?void 0:u.name,((y=s==null?void 0:s.booking)==null?void 0:y.type)&&((H=s==null?void 0:s.booking)==null?void 0:H.subtype)&&` • ${(V=s==null?void 0:s.booking)==null?void 0:V.type} ${(Z=s==null?void 0:s.booking)!=null&&Z.subtype?`• ${(B=s==null?void 0:s.booking)==null?void 0:B.subtype}`:""}`]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Fee"}),e.jsx("p",{className:"mt-1 font-medium",children:pe((Q=s==null?void 0:s.booking)==null?void 0:Q.price)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Bio"}),e.jsx("p",{className:"mt-1 text-sm",children:((G=s==null?void 0:s.user)==null?void 0:G.bio)||"No bio provided"})]})]})]})},re=new X,qe=({isOpen:f,onClose:r,user:s,booking:o,fetchRequestStatus:_})=>{const[D,z]=c.useState(""),[k,d]=c.useState(""),[j,b]=c.useState([]),[N,$]=c.useState(""),{dispatch:M}=c.useContext(q),[x,l]=c.useState(!1);console.log({customRequest:o});const v=()=>{N&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(N)&&(b([...j,N]),$(""))},P=i=>{b(j.filter(S=>S!==i))},L=async()=>{l(!0);try{const i=await re.callRawAPI("/v3/api/custom/courtmatchup/club/reservations/mail/custom-request",{club_id:o==null?void 0:o.club_id,to:"<EMAIL>",cc:j,user_id:s==null?void 0:s.id,booking_id:o==null?void 0:o.booking_id,subject:D,request:k},"POST");await ge(re,{user_id:localStorage.getItem("user"),activity_type:ee.custom,action_type:he.UPDATE,data:o,club_id:o==null?void 0:o.club_id,description:`Sent an email to ${s==null?void 0:s.email}`}),i.error?ie(M,i.message,5e3,"error"):(ie(M,"Email sent successfully",5e3),d(""),z(""),b([]),r(),_())}catch(i){console.error(i)}finally{l(!1)}};return e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${f?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-medium",children:"Send email"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-1 block text-gray-600",children:"Recipient email"}),e.jsx("div",{className:"rounded-lg border border-gray-200 px-3 py-2 text-gray-700",children:s==null?void 0:s.email})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-1 block text-gray-600",children:"CC (optional)"}),e.jsxs("div",{className:"space-y-2",children:[j.map((i,S)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex-1 rounded-lg border border-gray-300 px-3 py-2 text-gray-700",children:i}),e.jsx("button",{onClick:()=>P(i),className:"rounded-full p-1 text-gray-500 hover:bg-gray-100",children:e.jsx(De,{size:20})})]},S)),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("input",{type:"email",value:N,onChange:i=>$(i.target.value),placeholder:"Enter email address to CC",className:"flex-1 rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),e.jsx("button",{onClick:v,className:"rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700",children:"Add"})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"subject",className:"mb-1 block text-gray-600",children:"Subject"}),e.jsx("input",{type:"text",id:"subject",value:D,onChange:i=>z(i.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"message",className:"mb-1 block text-gray-600",children:"Message"}),e.jsx("textarea",{id:"message",value:k,onChange:i=>d(i.target.value),rows:6,className:"w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:r,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx($e,{onClick:L,className:"rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700",loading:x,children:"Send email"})]})]})]})},K=new X;function es({isOpen:f,onClose:r,customRequest:s}){var i,S,A,w,E,F,I,O;console.log(s);const[o,_]=c.useState(!1),[D,z]=c.useState(!1),[k,d]=c.useState(!1),[j,b]=c.useState(!1),[N,$]=c.useState([]),[M,x]=c.useState(!1),[l,v]=c.useState(null),P=async()=>{var u,y,H;try{d(!0);const V=await K.callRawAPI(`/v3/api/custom/courtmatchup/club/reservations/custom-request/${s==null?void 0:s.club_id}`,{request_id:s==null?void 0:s.id,to:(u=s==null?void 0:s.user)==null?void 0:u.email,subject:"Court Request Closed",body:"Your court request has been closed.",cc:[]},"POST");await ge(K,{user_id:localStorage.getItem("user"),activity_type:ee.custom,action_type:he.UPDATE,data:s,club_id:s==null?void 0:s.club_id,description:`Closed a custom request made by ${(y=s==null?void 0:s.user)==null?void 0:y.first_name} ${(H=s==null?void 0:s.user)==null?void 0:H.last_name}`}),d(!1)}catch(V){d(!1),console.log(V)}};async function L(){var u;try{b(!0),K.setTable("club_requests");const y=await K.callRestAPI({filter:[`booking_id,eq,${(u=s==null?void 0:s.booking)==null?void 0:u.id}`]},"GETALL");$(y.list)}catch(y){console.log(y)}finally{b(!1)}}return c.useEffect(()=>{var u;(u=s==null?void 0:s.booking)!=null&&u.id&&L()},[(i=s==null?void 0:s.booking)==null?void 0:i.id]),e.jsxs("div",{children:[j&&e.jsx(me,{}),e.jsxs("div",{className:"relative z-[99999]",children:[e.jsx(qe,{isOpen:o,onClose:()=>_(!1),user:s==null?void 0:s.user,booking:s,loading:D,fetchRequestStatus:L}),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${M?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-medium",children:"Email details"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Recipient email"}),e.jsx("div",{className:" text-gray-700",children:l==null?void 0:l.email})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Sent"}),e.jsxs("div",{className:" text-gray-700",children:[(l==null?void 0:l.create_at)&&ce(new Date(l.create_at),"MMMM d, yyyy")," ",(l==null?void 0:l.update_at)&&ce(new Date(l.update_at),"h:mm a")]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Subject"}),e.jsx("div",{className:" text-gray-700",children:l==null?void 0:l.subject})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Message"}),e.jsx("div",{className:" text-gray-700",children:l==null?void 0:l.request})]}),e.jsx("div",{className:"flex justify-end border-t pt-4",children:e.jsx("button",{onClick:()=>{x(!1),v(null)},className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Close"})})]})]})]}),e.jsx(xe,{title:"Custom request",isOpen:f,onClose:r,showFooter:!1,children:e.jsxs("div",{className:"space-y-6 ",children:[N.length>0&&e.jsxs("div",{className:"flex items-center justify-between rounded-xl bg-primaryBlue px-3 py-3 text-white",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 7.4V11H8.6V7.4H7.4ZM7.4 5V6.2H8.6V5H7.4Z",fill:"white"})})}),e.jsx("p",{className:"",children:"Email sent"})]}),e.jsx("button",{onClick:()=>{v(N[0]),x(!0)},className:"hover:underline",children:"View"})]}),e.jsxs("div",{className:"flex items-center justify-between rounded-xl bg-gray-100 px-2 py-2",children:[e.jsxs("p",{className:"text-lg font-medium",children:[" ",(S=s==null?void 0:s.user)==null?void 0:S.first_name," ",(A=s==null?void 0:s.user)==null?void 0:A.last_name]}),e.jsxs("div",{className:"flex items-center gap-2 rounded-xl border border-gray-200 px-2 py-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.00085 0.332031C7.36904 0.332031 7.66752 0.630508 7.66752 0.998698V2.9987C7.66752 3.36689 7.36904 3.66536 7.00085 3.66536C6.63266 3.66536 6.33418 3.36689 6.33418 2.9987V0.998698C6.33418 0.630508 6.63266 0.332031 7.00085 0.332031ZM11.7149 2.28465C11.9752 2.545 11.9752 2.96711 11.7149 3.22746L10.3007 4.64168C10.0403 4.90202 9.61822 4.90202 9.35787 4.64167C9.09752 4.38133 9.09752 3.95922 9.35787 3.69887L10.7721 2.28465C11.0324 2.0243 11.4545 2.0243 11.7149 2.28465ZM2.2868 2.28465C2.54715 2.0243 2.96926 2.0243 3.22961 2.28465L4.64383 3.69887C4.90418 3.95922 4.90418 4.38133 4.64383 4.64168C4.38348 4.90202 3.96137 4.90202 3.70102 4.64168L2.2868 3.22746C2.02645 2.96711 2.02645 2.545 2.2868 2.28465ZM0.333984 6.99915C0.333984 6.63096 0.632461 6.33248 1.00065 6.33248H3.00065C3.36884 6.33248 3.66732 6.63096 3.66732 6.99915C3.66732 7.36734 3.36884 7.66582 3.00065 7.66582H1.00065C0.632461 7.66582 0.333984 7.36734 0.333984 6.99915ZM10.334 6.99915C10.334 6.63096 10.6325 6.33248 11.0007 6.33248H13.0007C13.3688 6.33248 13.6673 6.63096 13.6673 6.99915C13.6673 7.36734 13.3688 7.66582 13.0007 7.66582H11.0007C10.6325 7.66582 10.334 7.36734 10.334 6.99915ZM4.64383 9.35572C4.90418 9.61607 4.90418 10.0382 4.64383 10.2985L3.22961 11.7127C2.96926 11.9731 2.54715 11.9731 2.2868 11.7127C2.02645 11.4524 2.02645 11.0303 2.2868 10.7699L3.70102 9.35572C3.96137 9.09537 4.38348 9.09537 4.64383 9.35572ZM9.35787 9.35572C9.61822 9.09537 10.0403 9.09537 10.3007 9.35572L11.7149 10.7699C11.9752 11.0303 11.9752 11.4524 11.7149 11.7127C11.4545 11.9731 11.0324 11.9731 10.7721 11.7127L9.35787 10.2985C9.09752 10.0382 9.09752 9.61607 9.35787 9.35572ZM7.00085 10.332C7.36904 10.332 7.66752 10.6305 7.66752 10.9987V12.9987C7.66752 13.3669 7.36904 13.6654 7.00085 13.6654C6.63266 13.6654 6.33418 13.3669 6.33418 12.9987V10.9987C6.33418 10.6305 6.63266 10.332 7.00085 10.332Z",fill:"#375DFB"})})}),e.jsx("span",{className:"text-gray-600",children:"Open"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Requester"}),e.jsxs("p",{className:"font-medium",children:[(w=s==null?void 0:s.user)==null?void 0:w.first_name," ",(E=s==null?void 0:s.user)==null?void 0:E.last_name]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Time"}),e.jsxs("p",{className:"font-medium",children:[ne((F=s==null?void 0:s.booking)==null?void 0:F.start_time)," -"," ",ne((I=s==null?void 0:s.booking)==null?void 0:I.end_time)]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Coach preference"}),e.jsx("p",{className:"font-medium",children:"Pete Sampras"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-gray-600",children:"Request"}),e.jsx("p",{className:"font-medium",children:(O=s==null?void 0:s.booking)==null?void 0:O.notes})]})]}),e.jsxs("div",{className:"fixed bottom-0 left-0 right-0 flex gap-4 border-t border-gray-200 bg-white p-4",children:[e.jsxs("button",{onClick:P,disabled:k,className:"flex flex-1 items-center justify-center gap-3 rounded-lg border border-gray-300 px-4 py-2 font-medium text-gray-700 hover:bg-gray-50",children:[e.jsx("span",{children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.4993 6.91797L7.74935 11.5013L6.08268 9.83464M16.7077 9.0013C16.7077 13.2585 13.2565 16.7096 8.99935 16.7096C4.74215 16.7096 1.29102 13.2585 1.29102 9.0013C1.29102 4.74411 4.74215 1.29297 8.99935 1.29297C13.2565 1.29297 16.7077 4.74411 16.7077 9.0013Z",stroke:"#162664","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("span",{children:k?"Closing...":"Close request"})]}),e.jsxs("button",{onClick:()=>{_(!0)},className:"flex flex-1 items-center justify-center gap-3 rounded-lg bg-primaryBlue px-4 py-2 font-medium text-white hover:bg-blue-800",children:[e.jsx("span",{children:e.jsx("svg",{width:"17",height:"14",viewBox:"0 0 17 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.25 9.5H6.75C5.5197 9.49953 4.31267 9.83536 3.25941 10.4712C2.20614 11.107 1.3467 12.0186 0.774001 13.1075C0.757903 12.9054 0.749897 12.7027 0.750001 12.5C0.750001 8.35775 4.10775 5 8.25 5V0.875L16.125 7.25L8.25 13.625V9.5ZM6.75 8H9.75V10.481L13.7408 7.25L9.75 4.019V6.5H8.25C7.38769 6.49903 6.53535 6.68436 5.75129 7.04332C4.96724 7.40227 4.26999 7.92637 3.70725 8.57975C4.67574 8.19591 5.70822 7.99919 6.75 8Z",fill:"white"})})}),e.jsx("span",{children:"Send email"})]})]})]})})]})}let oe=new X,ss=new Ae;const as=[{header:"Date",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"By",accessor:"user",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{1:"Paid",0:"Reserved",2:"Failed"}}],ls=({sports:f,club:r,courts:s})=>{const{dispatch:o,state:_}=t.useContext(q),{dispatch:D}=t.useContext(Fe),[z,k]=t.useState([]),[d,j]=t.useState(10),[b,N]=t.useState(0),[$,M]=t.useState(0),[x,l]=t.useState(1),[v,P]=t.useState(!1),[L,i]=t.useState(!1),[S,A]=t.useState(!1);t.useState(!1),t.useState([]),t.useState([]),t.useState("eq");const[w,E]=t.useState(!0),[F,I]=t.useState(!1),[O,u]=t.useState(!1);t.useState(),Te();const y=t.useRef(null),[H,V]=t.useState(!1),[Z,B]=t.useState(null);c.useState(!1);const[Q,G]=c.useState(!1),[T,C]=c.useState(!1);c.useState(null);const[J,se]=c.useState(null);c.useState([]);const[fe,ue]=c.useState([]),ye=ze({id:U(),email:U(),role:U(),status:U()}),{register:je,handleSubmit:be,formState:{errors:ts}}=Ie({resolver:Ze(ye)});function Ne(){p(x-1,d)}function Se(){p(x+1,d)}const Ce=async()=>{try{oe.setTable("user");const a=await oe.callRestAPI({filter:[`club_id,eq,${r==null?void 0:r.id}`,"role,cs,user"]},"GETALL");ue(a.list||[])}catch(a){console.error("Error fetching players:",a),showToast(o,"Error fetching players",3e3,"error")}};async function p(a,n,g={},h=[]){E(!(O||F));try{const m=await ss.getPaginate("reservation",{page:a,limit:n,filter:[...h,`courtmatchup_reservation.club_id,cs,${r==null?void 0:r.id}`,"courtmatchup_booking.custom_request,cs,1"],join:["clubs|club_id","booking|booking_id","user|user_id"],size:d}),Ee=m.list;if(m){const Me=Ee.map(le=>{const te=f.find(Pe=>Pe.id===le.sport_id);return{...le,sport:te?te.name:"--"}});E(!1),k(Me),j(m.limit),N(m.num_pages),l(m.page),M(m.total),P(m.page>1),i(m.page+1<=m.num_pages)}}catch(m){E(!1),console.log("ERROR",m),He(D,m.message)}}const _e=a=>{a.search?p(1,d,{},[`first_name,cs,${a.search}`,`last_name,cs,${a.search}`]):p(1,d)},ke=async a=>{const n=a.target.value;n===""?await p(1,d):await p(1,d,{},[`sport_id,eq,${parseInt(n)}`])},ve=async a=>{a.target.value===""?await p(x,d):await p(x,d,{},[`courtmatchup_booking.reservation_type,cs,${a.target.value}`])},Le=async a=>{a.target.value===""?await p(x,d):await p(x,d,{},[`courtmatchup_booking.status,cs,${a.target.value}`])};t.useEffect(()=>{o({type:"SETPATH",payload:{path:"reservations"}}),r!=null&&r.id&&(p(1,d,{}),Ce())},[r==null?void 0:r.id]);const ae=a=>{y.current&&!y.current.contains(a.target)&&A(!1)};t.useEffect(()=>(document.addEventListener("mousedown",ae),()=>{document.removeEventListener("mousedown",ae)}),[]);const we={type:a=>{var n;return e.jsx("span",{className:"capitalize",children:((n=de.find(g=>{var h;return g.value==((h=a==null?void 0:a.booking)==null?void 0:h.reservation_type)}))==null?void 0:n.label)||"--"})},sport:a=>{var n;return e.jsx("span",{className:"capitalize",children:((n=f.find(g=>{var h;return g.id===((h=a==null?void 0:a.booking)==null?void 0:h.sport_id)}))==null?void 0:n.name)||"--"})},date:a=>{var n,g,h;return e.jsxs(e.Fragment,{children:[R((n=a==null?void 0:a.booking)==null?void 0:n.date)," "," | "," ",Y((g=a==null?void 0:a.booking)==null?void 0:g.start_time)," "," - "," ",Y((h=a==null?void 0:a.booking)==null?void 0:h.end_time)]})},players:a=>{var n,g;return e.jsx(e.Fragment,{children:(n=a==null?void 0:a.booking)!=null&&n.player_ids?`${JSON.parse((g=a==null?void 0:a.booking)==null?void 0:g.player_ids).length} players`:"0 players"})},bill:a=>{var n;return e.jsx(e.Fragment,{children:pe((n=a==null?void 0:a.booking)==null?void 0:n.price)})},user:a=>{var n,g,h,m;return e.jsx(e.Fragment,{children:!((n=a==null?void 0:a.user)!=null&&n.first_name)||!((g=a==null?void 0:a.user)!=null&&g.last_name)?"--":`${(h=a==null?void 0:a.user)==null?void 0:h.first_name} ${(m=a==null?void 0:a.user)==null?void 0:m.last_name}`})},status:a=>e.jsxs(e.Fragment,{children:[a.booking.status==W.SUCCESS&&e.jsx(Ue,{}),a.booking.status==W.PENDING&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Ke,{}),e.jsx(Ye,{timeLeft:Ve(a==null?void 0:a.reservation_updated_at)})]}),a.booking.status==W.FAIL&&e.jsx(Xe,{})]})};return e.jsxs("div",{className:"h-screen px-8",children:[w&&e.jsx(Je,{}),e.jsxs("div",{className:"flex flex-col gap-4 py-3",children:[e.jsxs("div",{className:"flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0",children:[e.jsxs("form",{className:"relative flex w-full items-center md:max-w-sm",onSubmit:be(_e),children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(Oe,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search users",...je("search")})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 sm:gap-4",children:[e.jsx("input",{type:"date",className:"w-full rounded-lg border border-gray-200 text-sm text-gray-500 sm:w-auto"}),e.jsx("input",{type:"time",defaultValue:"00:00",className:"w-full rounded-lg border border-gray-200 text-sm text-gray-500 sm:w-auto"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5",children:[e.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"Sport: All",onChange:ke,children:[e.jsx("option",{value:"",children:"Sport: All"}),f==null?void 0:f.map(a=>e.jsx("option",{value:a.id,children:a.name},a.id))]}),e.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"All",onChange:Le,children:[e.jsx("option",{value:"",children:"Status: All"}),e.jsx("option",{value:"0",children:"Reserved"}),e.jsx("option",{value:"1",children:"Paid"}),e.jsx("option",{value:"2",children:"Failed"})]}),e.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"All",onChange:ve,children:[e.jsx("option",{value:"",children:"Reservation Type: All"}),de.map(a=>e.jsx("option",{value:a.value,children:a.label},a.value))]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(We,{title:"Custom Request History",emptyMessage:"No custom request history found",activityType:ee.custom})})]})]}),w?e.jsx(Ge,{}):e.jsx("div",{className:"overflow-x-auto",children:e.jsx(Qe,{columns:as,data:z,loading:w,renderCustomCell:we,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",onClick:a=>se(a)})}),e.jsx(Be,{currentPage:x,pageCount:b,pageSize:d,canPreviousPage:v,canNextPage:L,updatePageSize:a=>{j(a),p(1,a)},previousPage:Ne,nextPage:Se,gotoPage:a=>p(a,d)}),e.jsx(Re,{isOpen:H,onClose:()=>V(!1),reservation:Z,userList:fe,sports:f,setReservation:B,getData:p,setShowCheckin:G,setShowCheckout:C,courts:s}),e.jsx(es,{isOpen:!!J,onClose:()=>se(null),customRequest:J})]})},js=ls;export{js as L};
