import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as c,b as A}from"./vendor-851db8c1.js";import{G as me,v as oe,R as _,M as he,a3 as ee}from"./index-5b566256.js";import{f as b,k as ge,e as pe,l as z,m as L}from"./date-fns-07266b7d.js";function je({showDatePicker:t,setShowDatePicker:h,dateRange:x,setDateRange:j,currentMonth:g,setCurrentMonth:d,selectionStart:p,setSelectionStart:C,selectionEnd:N,setSelectionEnd:v,hoverDate:D,setHoverDate:H}){const[T,k]=c.useState(null),w=s=>{if(!p)return!1;const n=p,l=N||D;if(!l)return s.getTime()===n.getTime();const i=s.getTime(),f=Math.min(n.getTime(),l.getTime()),V=Math.max(n.getTime(),l.getTime());return i>=f&&i<=V},B=s=>{const n=new Date(s.getFullYear(),s.getMonth(),1),l=new Date(s.getFullYear(),s.getMonth()+1,0),i=[];for(let f=0;f<n.getDay();f++)i.push({date:new Date(n.getFullYear(),n.getMonth(),-f),disabled:!0});for(let f=1;f<=l.getDate();f++)i.push({date:new Date(n.getFullYear(),n.getMonth(),f),disabled:!1});return i},y=s=>{const n=new Date;let l=new Date;switch(k(s),s){case"7days":l=L(n,6);break;case"30days":l=L(n,29);break;case"3months":l=z(n,3);break;case"12months":l=z(n,12);break;case"monthToDate":l=pe(n);break;case"yearToDate":l=ge(n);break;case"allTime":l=new Date(2020,0,1);break;case"today":l=new Date;break;default:return}C(l),v(n),j({start:l,end:n})},M=s=>{if(!x.start||!x.end)return!1;const n=new Date;let l=new Date;switch(s){case"7days":l=L(n,6);break;case"30days":l=L(n,29);break;case"3months":l=z(n,3);break;default:return!1}return x.start.getTime()===l.getTime()&&x.end.getTime()===n.getTime()};return t?e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50 px-2",children:e.jsxs("div",{className:"flex max-h-[90vh] w-full max-w-[800px] flex-col overflow-auto rounded-lg bg-white sm:flex-row",children:[e.jsxs("div",{className:"flex w-full flex-col justify-between gap-4 border-b border-gray-200 p-6 !text-left sm:w-[200px] sm:border-b-0 sm:border-r",children:[e.jsx("button",{className:`gap-2 !text-left text-lg font-medium transition-colors ${T==="today"?"text-primaryBlue":"hover:text-primaryBlue"}`,onClick:()=>y("today"),children:"Today"}),e.jsxs("div",{className:"flex flex-col gap-4 !text-left",children:[e.jsxs("button",{className:`py-2 !text-left transition-colors ${M("7days")?"font-medium text-primaryBlue":"hover:text-primaryBlue"}`,onClick:()=>y("7days"),children:["Last 7 days",M("7days")&&e.jsx("span",{className:"ml-2 text-xs",children:"✓"})]}),e.jsxs("button",{className:`py-2 !text-left transition-colors ${M("30days")?"font-medium text-primaryBlue":"hover:text-primaryBlue"}`,onClick:()=>y("30days"),children:["Last 30 days",M("30days")&&e.jsx("span",{className:"ml-2 text-xs",children:"✓"})]}),e.jsxs("button",{className:`py-2 !text-left transition-colors ${M("3months")?"font-medium text-primaryBlue":"hover:text-primaryBlue"}`,onClick:()=>y("3months"),children:["Last 3 months",M("3months")&&e.jsx("span",{className:"ml-2 text-xs",children:"✓"})]}),e.jsx("button",{className:"py-2 !text-left hover:text-primaryBlue",onClick:()=>y("12months"),children:"Last 12 months"}),e.jsx("button",{className:"py-2 !text-left hover:text-primaryBlue",onClick:()=>y("monthToDate"),children:"Month to date"}),e.jsx("button",{className:"py-2 !text-left hover:text-primaryBlue",onClick:()=>y("yearToDate"),children:"Year to date"}),e.jsx("button",{className:"py-2 !text-left hover:text-primaryBlue",onClick:()=>y("allTime"),children:"All time"})]})]}),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between",children:[e.jsxs("div",{className:"w-full border-b border-gray-200 p-4 sm:w-1/2 sm:border-r",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between pt-6",children:[e.jsx("button",{onClick:()=>d(new Date(g.getFullYear(),g.getMonth()-1)),children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M15 18l-6-6 6-6",stroke:"currentColor",strokeWidth:"2"})})}),e.jsx("span",{className:"text-lg font-medium",children:b(g,"MMM, yyyy")}),e.jsx("button",{className:"sm:hidden",onClick:()=>d(new Date(g.getFullYear(),g.getMonth()+1)),children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M9 18l6-6-6-6",stroke:"currentColor",strokeWidth:"2"})})}),e.jsx("div",{className:"hidden w-6 sm:block"})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-2",children:[["M","T","W","T","F","S","S"].map(s=>e.jsx("div",{className:"text-center text-sm text-gray-500",children:s},s)),B(g).map((s,n)=>e.jsxs("button",{className:`relative rounded-lg p-2 text-center ${s.disabled?"text-gray-300":w(s.date)?"bg-[#EBF1FF] text-[#375dfb] hover:bg-opacity-20":"hover:bg-blue-50"} ${p&&s.date.getTime()===p.getTime()?"!bg-[#375dfb] !text-white":""} ${N&&s.date.getTime()===N.getTime()?"!bg-[#375dfb] !text-white":""}`,disabled:s.disabled,onMouseEnter:()=>!s.disabled&&H(s.date),onClick:()=>{if(!s.disabled)if(!p||N)C(s.date),v(null),j({start:s.date,end:null});else{const l=p,i=s.date;i<l?(C(i),v(l),j({start:i,end:l})):(v(i),j({start:l,end:i}))}},children:[b(s.date,"d"),w(s.date)&&e.jsxs(e.Fragment,{children:[s.date.getDate()!==1&&e.jsx("div",{className:"absolute left-0 top-0 h-full w-1/2 bg-primaryBlue bg-opacity-10"}),s.date.getDate()!==new Date(s.date.getFullYear(),s.date.getMonth()+1,0).getDate()&&e.jsx("div",{className:"absolute right-0 top-0 h-full w-1/2 bg-primaryBlue bg-opacity-10"})]})]},n))]})]}),e.jsxs("div",{className:"hidden w-1/2 border-b border-gray-200 p-4 sm:block",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between pt-6",children:[e.jsx("div",{className:"w-6"}),e.jsx("span",{className:"text-lg font-medium",children:b(new Date(g.getFullYear(),g.getMonth()+1),"MMM, yyyy")}),e.jsx("button",{onClick:()=>d(new Date(g.getFullYear(),g.getMonth()+1)),children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M9 18l6-6-6-6",stroke:"currentColor",strokeWidth:"2"})})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-2",children:[["M","T","W","T","F","S","S"].map(s=>e.jsx("div",{className:"text-center text-sm text-gray-500",children:s},s)),B(new Date(g.getFullYear(),g.getMonth()+1)).map((s,n)=>e.jsxs("button",{className:`relative rounded-lg p-2 text-center ${s.disabled?"text-gray-300":w(s.date)?"bg-[#EBF1FF] text-[#375dfb] hover:bg-opacity-20":"hover:bg-blue-50"} ${p&&s.date.getTime()===p.getTime()?"bg-[#375dfb] !text-white":""} ${N&&s.date.getTime()===N.getTime()?"bg-[#375dfb] !text-white":""}`,disabled:s.disabled,onMouseEnter:()=>!s.disabled&&H(s.date),onClick:()=>{if(!s.disabled)if(!p||N)C(s.date),v(null),j({start:s.date,end:null});else{const l=p,i=s.date;i<l?(C(i),v(l),j({start:i,end:l})):(v(i),j({start:l,end:i}))}},children:[b(s.date,"d"),w(s.date)&&e.jsxs(e.Fragment,{children:[s.date.getDate()!==1&&e.jsx("div",{className:"absolute left-0 top-0 h-full w-1/2 bg-primaryBlue bg-opacity-10"}),s.date.getDate()!==new Date(s.date.getFullYear(),s.date.getMonth()+1,0).getDate()&&e.jsx("div",{className:"absolute right-0 top-0 h-full w-1/2 bg-primaryBlue bg-opacity-10"})]})]},n))]})]})]}),e.jsxs("div",{className:"mt-6 flex flex-col gap-4 p-4 sm:flex-row sm:items-center sm:justify-between sm:p-6",children:[e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-gray-500",children:"Range: "}),x.start?b(x.start,"MMM d, yyyy"):""," -",x.end?b(x.end,"MMM d, yyyy"):""]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{className:"flex-1 rounded-lg border border-gray-300 px-4 py-2 sm:flex-initial",onClick:()=>{h(!1),C(null),v(null),H(null)},children:"Cancel"}),e.jsx("button",{className:"flex-1 rounded-lg bg-primaryBlue px-4 py-2 text-white sm:flex-initial",onClick:()=>{h(!1)},children:"Apply"})]})]})]})]})}):null}new he;const fe=({hours:t})=>{var h,x,j;return e.jsxs("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-5",children:[e.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2",children:[e.jsx("p",{className:"text-lg",children:"Hours"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Schedule"})]}),e.jsxs("div",{className:"px-4 pb-4 pt-2",children:[e.jsxs("div",{className:"mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.0007 7.29232V10.0007L11.2507 11.2507M4.79232 16.0423H15.209C15.6692 16.0423 16.0423 15.6692 16.0423 15.209V4.79232C16.0423 4.33208 15.6692 3.95898 15.209 3.95898H4.79232C4.33208 3.95898 3.95898 4.33208 3.95898 4.79232V15.209C3.95898 15.6692 4.33208 16.0423 4.79232 16.0423ZM5.83398 3.95898L6.47738 2.02879C6.59081 1.68851 6.90926 1.45898 7.26795 1.45898H12.7334C13.092 1.45898 13.4105 1.68851 13.5239 2.02879L14.1673 3.95898H5.83398ZM5.83398 16.0423L6.47738 17.9725C6.59081 18.3128 6.90926 18.5423 7.26795 18.5423H12.7334C13.092 18.5423 13.4105 18.3128 13.5239 17.9725L14.1673 16.0423H5.83398Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:"Available"}),e.jsx("span",{className:"ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white"})]}),e.jsx("div",{className:"flex flex-col divide-y",children:((h=t==null?void 0:t.available)==null?void 0:h.length)>0?e.jsx("div",{className:"flex items-center justify-between py-4",children:e.jsx("div",{className:"flex gap-2",children:e.jsxs("p",{children:[ee((x=t==null?void 0:t.range)==null?void 0:x.from)," -"," ",ee((j=t==null?void 0:t.range)==null?void 0:j.until)]})})}):e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available hours"})})})]})]})};function ye({clubAvailability:t,fetchClubAvailability:h,sports:x}){var Y,$,O,P,W,Z,I,U,E,q,G,K,J,Q;const{dispatch:j,state:g}=A.useContext(me);A.useEffect(()=>{j({type:"SETPATH",payload:{path:"availability"}})},[]);const[d,p]=c.useState(!0),[C,N]=c.useState(!1),[v,D]=c.useState(!1),[H,T]=c.useState(!1),[k,w]=c.useState(null);c.useEffect(()=>{const a=r=>{k!==null&&!r.target.closest(".relative")&&w(null)};return document.addEventListener("mousedown",a),()=>document.removeEventListener("mousedown",a)},[k]);const[B,y]=c.useState(!1),[M,s]=c.useState({start:null,end:null}),[n,l]=c.useState(new Date),[i,f]=c.useState(null),[V,se]=c.useState(null),[te,ae]=c.useState(null),u=d?(Y=t==null?void 0:t.courts)==null?void 0:Y.available:($=t==null?void 0:t.courts)==null?void 0:$.unavailable,m=d?(O=t==null?void 0:t.staff)==null?void 0:O.available:(P=t==null?void 0:t.staff)==null?void 0:P.unavailable,o=d?(W=t==null?void 0:t.coaches)==null?void 0:W.available:(Z=t==null?void 0:t.coaches)==null?void 0:Z.unavailable,le=t==null?void 0:t.hours;console.log({clubAvailability:t});const[F,ne]=c.useState(b(new Date,"yyyy-MM-dd")),[S,re]=c.useState(""),de=a=>{const r=a.target.value;ne(r),h({date:r,time:S,is_available:d})},ie=a=>{const r=a.target.value;re(r),h(r?{date:F,time:`${r}:00`,is_available:d}:{date:F,is_available:d})},xe=a=>{const r=a.target.value;h(r===""?{is_available:d}:{sport_id:r,is_available:d})};return e.jsxs("div",{className:"flex min-h-screen flex-col gap-6 p-5",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",id:"available",checked:d,onChange:()=>{p(!0),h({is_available:!0})},className:"text-blue-600"}),e.jsx("label",{htmlFor:"available",children:"Available"}),e.jsx("input",{type:"radio",id:"unavailable",checked:!d,onChange:()=>{p(!1),h({is_available:!1})},className:"ml-4 text-blue-600"}),e.jsx("label",{htmlFor:"unavailable",children:"Unavailable"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:xe,children:[e.jsx("option",{children:"Sport: All"}),x==null?void 0:x.map(a=>e.jsx("option",{value:a.id,children:a.name}))]}),e.jsx("input",{type:"date",value:F,onChange:de,className:"rounded-lg border border-gray-300 px-3 py-1 text-gray-500 outline-gray-500"}),e.jsx("input",{type:"time",value:S,onChange:ie,className:"rounded-lg border border-gray-300 px-3 py-1 text-gray-500 outline-gray-500"})]})]}),e.jsx(je,{showDatePicker:B,setShowDatePicker:y,dateRange:M,setDateRange:s,currentMonth:n,setCurrentMonth:l,selectionStart:i,setSelectionStart:f,selectionEnd:V,setSelectionEnd:se,hoverDate:te,setHoverDate:ae}),e.jsxs("div",{className:"flex w-full flex-col gap-6 md:w-full md:flex-row",children:[e.jsxs("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-5",children:[e.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2",children:[e.jsx("p",{className:"text-lg",children:"Courts"}),e.jsx("button",{onClick:()=>N(!0),className:"text-sm text-gray-600",children:"All courts"})]}),e.jsxs("div",{className:"px-4 pb-4 pt-2",children:[e.jsxs("div",{className:"mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.45833 3.95768V2.29102M13.5417 3.95768V2.29102M7.70833 10.6452L9.08333 12.0202L12.2917 8.81186M3.95833 16.8743H16.0417C16.5019 16.8743 16.875 16.5013 16.875 16.041V4.79102C16.875 4.33078 16.5019 3.95768 16.0417 3.95768H3.95833C3.4981 3.95768 3.125 4.33078 3.125 4.79102V16.041C3.125 16.5013 3.4981 16.8743 3.95833 16.8743Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:d?"Available":"Unavailable"}),e.jsx("span",{className:"ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white",children:d?((U=(I=t==null?void 0:t.courts)==null?void 0:I.available)==null?void 0:U.length)||0:((q=(E=t==null?void 0:t.courts)==null?void 0:E.unavailable)==null?void 0:q.length)||0})]}),e.jsx("div",{className:"flex flex-col divide-y",children:(u==null?void 0:u.length)>0?u.map((a,r)=>e.jsx("div",{className:"flex items-center justify-between py-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("span",{children:[(a==null?void 0:a.name)||"Court"," "]}),e.jsxs("span",{className:"flex items-center justify-center rounded-xl bg-gray-100 px-2 text-xs text-gray-500",children:["#",a==null?void 0:a.id]})]})})):e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available courts"})})})]})]}),e.jsx(fe,{hours:le}),e.jsxs("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-5",children:[e.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2",children:[e.jsx("p",{className:"text-lg",children:"Coaches"}),e.jsx("button",{onClick:()=>D(!0),className:"text-sm text-gray-600",children:"All"})]}),e.jsxs("div",{className:"px-4 pb-4 pt-2",children:[e.jsxs("div",{className:"mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.3743 5.62435V3.12435C14.3743 2.66411 14.0013 2.29102 13.541 2.29102H3.12435C2.66411 2.29102 2.29102 2.66411 2.29102 3.12435V13.541C2.29102 14.0013 2.66411 14.3743 3.12435 14.3743H5.62435M7.77124 17.7077C8.15529 15.8028 9.75317 14.3743 11.666 14.3743C13.5789 14.3743 15.1767 15.8028 15.5608 17.7077M7.77124 17.7077H6.45768C5.99745 17.7077 5.62435 17.3346 5.62435 16.8743V6.45768C5.62435 5.99745 5.99745 5.62435 6.45768 5.62435H16.8743C17.3346 5.62435 17.7077 5.99745 17.7077 6.45768V16.8743C17.7077 17.3346 17.3346 17.7077 16.8743 17.7077H15.5608M7.77124 17.7077H15.5608M13.541 10.416C13.541 11.4516 12.7016 12.291 11.666 12.291C10.6305 12.291 9.79102 11.4516 9.79102 10.416C9.79102 9.38048 10.6305 8.54102 11.666 8.54102C12.7016 8.54102 13.541 9.38048 13.541 10.416Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})}),e.jsx("span",{children:d?"Available":"Unavailable"}),e.jsx("span",{className:"ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white",children:d?(o==null?void 0:o.length)||0:((K=(G=t==null?void 0:t.coaches)==null?void 0:G.unavailable)==null?void 0:K.length)||0})]}),e.jsx("div",{className:"flex flex-col divide-y",children:(o==null?void 0:o.length)>0?o.map((a,r)=>{var R;return e.jsxs("div",{className:"relative flex items-center gap-2 py-3",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-200",children:e.jsx("img",{src:a.photo||"/default-avatar.png",alt:"coach",className:"h-full w-full rounded-full object-cover"})}),e.jsxs("span",{children:[a.first_name||"N/A"," ",a.last_name||""]}),e.jsx("button",{className:"ml-auto",onClick:()=>w(k===r?null:r),children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})})}),k===r&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-64 rounded-lg bg-white p-4 shadow-lg",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 font-medium",children:"Contact information"}),e.jsxs("div",{className:"space-y-2 rounded-lg bg-gray-50 p-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"EMAIL"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:a.email||"N/A"}),e.jsx("button",{className:"ml-auto",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M15 6.667H8.333v1.666H15V6.667zm0 3.333H8.333v1.667H15V10zm-6.667 3.333H15V15H8.333v-1.667zM6.667 6.667H5v1.666h1.667V6.667zm0 3.333H5v1.667h1.667V10zm0 3.333H5V15h1.667v-1.667z",fill:"#868C98"})})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"HOURLY RATE"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{className:"text-sm",children:a.hourly_rate&&a.hourly_rate>0?oe(a.hourly_rate):"N/A"})})]}),a.bio&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"BIO"}),e.jsx("div",{className:"text-sm",children:a.bio})]}),((R=a.sports)==null?void 0:R.length)>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"SPORTS"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:a.sports.map((X,ce)=>e.jsxs("span",{className:"rounded-lg bg-gray-100 px-2 py-1 text-xs text-gray-600",children:[X.sport_name," ($",X.price,")"]},ce))})]})]})]}),e.jsx("button",{className:"w-full rounded-lg bg-primaryBlue py-2 text-center text-white",onClick:()=>w(null),children:"Close"})]})]},r)}):e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available coaches"})})})]})]}),e.jsxs("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-5",children:[e.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2",children:[e.jsx("p",{className:"text-lg",children:"Staff working"}),e.jsx("button",{onClick:()=>T(!0),className:"text-sm text-gray-600",children:"All"})]}),e.jsxs("div",{className:"px-4 pb-4 pt-2",children:[e.jsxs("div",{className:"mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1",children:[e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{clipPath:"url(#clip0_40000074_42565)",children:e.jsx("path",{d:"M12.4996 2.29102C14.1104 2.29102 15.4162 3.59685 15.4162 5.20768C15.4162 6.81851 14.1104 8.12435 12.4996 8.12435M17.2912 16.8743H18.5412C19.0015 16.8743 19.3808 16.4989 19.3088 16.0444C18.9721 13.9194 17.3725 12.0233 15.2079 11.2567M7.70789 8.12435C6.09706 8.12435 4.79123 6.81851 4.79123 5.20768C4.79123 3.59685 6.09706 2.29102 7.7079 2.29102C9.31873 2.29102 10.6246 3.59685 10.6246 5.20768C10.6246 6.81851 9.31872 8.12435 7.70789 8.12435ZM0.89718 16.0181C1.35728 13.0571 4.23121 10.6243 7.7079 10.6243C11.1846 10.6243 14.0585 13.0571 14.5186 16.0181C14.5893 16.4728 14.2098 16.8483 13.7496 16.8483H1.66623C1.20599 16.8483 0.826512 16.4728 0.89718 16.0181Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_40000074_42565",children:e.jsx("rect",{width:"20",height:"20",fill:"white"})})})]}),e.jsx("span",{children:d?"Available":"Unavailable"}),e.jsx("span",{className:"ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white",children:d?(m==null?void 0:m.length)||0:((Q=(J=t==null?void 0:t.staff)==null?void 0:J.unavailable)==null?void 0:Q.length)||0})]}),e.jsx("div",{className:"flex flex-col divide-y",children:(m==null?void 0:m.length)>0?m.map((a,r)=>e.jsxs("div",{className:"relative flex items-center gap-2 py-3",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-200",children:e.jsx("img",{src:a.photo||"/default-avatar.png",alt:"staff",className:"h-full w-full rounded-full object-cover"})}),e.jsxs("span",{children:[a.first_name," ",a.last_name]}),e.jsx("button",{className:"ml-auto",onClick:()=>w(k===r?null:r),children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})})}),k===r&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-64 rounded-lg bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-2 text-lg text-gray-500",children:"Contact information"}),e.jsxs("div",{className:"space-y-2 rounded-lg bg-gray-50 p-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"EMAIL"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:a.email}),e.jsx("button",{className:"ml-auto",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M15 6.667H8.333v1.666H15V6.667zm0 3.333H8.333v1.667H15V10zm-6.667 3.333H15V15H8.333v-1.667zM6.667 6.667H5v1.666h1.667V6.667zm0 3.333H5v1.667h1.667V10zm0 3.333H5V15h1.667v-1.667z",fill:"#868C98"})})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"PHONE"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:a.phone?a.phone:"N/A"}),e.jsx("button",{className:"ml-auto",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M15 6.667H8.333v1.666H15V6.667zm0 3.333H8.333v1.667H15V10zm-6.667 3.333H15V15H8.333v-1.667zM6.667 6.667H5v1.666h1.667V6.667zm0 3.333H5v1.667h1.667V10zm0 3.333H5V15h1.667v-1.667z",fill:"#868C98"})})})]})]})]})]}),e.jsx("button",{className:"w-full rounded-lg bg-primaryBlue py-2 text-center text-white",onClick:()=>w(null),children:"Close"})]})]},r)):e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available staff"})})})]})]})]}),e.jsx(_,{isOpen:C,onClose:()=>N(!1),title:"All courts",showFooter:!1,children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Date and time"}),e.jsx("span",{className:"text-base",children:b(new Date,"MMM dd, yyyy • hh:mm a")})]}),e.jsxs("div",{className:"flex flex-col gap-2 divide-y",children:[(u==null?void 0:u.length)>0&&u.map((a,r)=>e.jsxs("div",{className:"flex items-center justify-between pt-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{className:"text-sm font-medium",children:a.name||`Court ${r+1}`})}),e.jsx("span",{className:"text-sm text-gray-500",children:"Available"})]},r)),(u==null?void 0:u.length)===0&&e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available courts"})})]})]})})})}),e.jsx(_,{isOpen:H,onClose:()=>T(!1),title:"All staff",showFooter:!1,children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Date and time"}),e.jsx("span",{className:"text-base",children:b(new Date,"MMM dd, yyyy • hh:mm a")})]}),e.jsxs("div",{className:"flex flex-col gap-2 divide-y",children:[(m==null?void 0:m.length)>0&&m.map((a,r)=>e.jsxs("div",{className:"flex items-center justify-between pt-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-sm font-medium",children:[a.first_name," ",a.last_name]})}),e.jsx("span",{className:"text-sm text-gray-500",children:"Available"})]},r)),(m==null?void 0:m.length)===0&&e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available staff"})})]})]})})})}),e.jsx(_,{isOpen:v,onClose:()=>D(!1),title:"All coaches",showFooter:!1,children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Date and time"}),e.jsx("span",{className:"text-base",children:b(new Date,"MMM dd, yyyy • hh:mm a")})]}),e.jsxs("div",{className:"flex flex-col gap-2 divide-y",children:[(o==null?void 0:o.length)>0&&o.map((a,r)=>e.jsxs("div",{className:"flex items-center justify-between pt-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-sm font-medium",children:[a.first_name," ",a.last_name]})}),e.jsx("span",{className:"text-sm text-gray-500",children:"Available"})]},r)),(o==null?void 0:o.length)===0&&e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("p",{className:"text-sm text-gray-500",children:"No available coaches"})})]})]})})})})]})}export{ye as A};
