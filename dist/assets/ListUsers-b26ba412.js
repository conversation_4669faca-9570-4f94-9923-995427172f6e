import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as Q,b as a}from"./vendor-851db8c1.js";import{M as W,G as X,A as ee,E as M,t as se,d as te,H as ae,J as re,b as S}from"./index-5b566256.js";import{P as le}from"./index-eb1bc208.js";import{_ as P}from"./lodash-91d5d207.js";import ne from"./Skeleton-1e8bf077.js";import{H as oe}from"./HistoryComponent-4f475aec.js";import{D as ie}from"./DataTable-a2248415.js";import{F as de}from"./FormattedPhoneNumber-40dd7178.js";let l=new W;const ce=[{header:"Name",accessor:"name"},{header:"Email",accessor:"email"},{header:"Phone number",accessor:"phone_number"},{header:"Membership status",accessor:"slug"},{header:"NTRP",accessor:"ntrp"},{header:"",accessor:"actions"}],me=({club:n})=>{const E=Q(),{dispatch:H}=a.useContext(X),{dispatch:h}=a.useContext(ee),[Z,$]=a.useState([]),[d,j]=a.useState(10),[N,V]=a.useState(0),[x,k]=a.useState(0),[T,D]=a.useState(!1),[A,R]=a.useState(!1),[t,u]=a.useState({firstName:"",lastName:"",email:"",phone:"",ageGroup:"",status:"",ntrp:"",membership:""}),[v,f]=a.useState(!0),[F,G]=a.useState(!1),C=a.useRef(null),[I,_]=a.useState(!1),[g,y]=a.useState(null),U=localStorage.getItem("user"),z=localStorage.getItem("role");function O(){c(x-1,d)}function B(){c(x+1,d)}async function c(s,r,p={},m=[]){f(!F),console.log("filters",m);try{l.setTable("profile");const i=await l.callRestAPI({payload:{...p},page:s,limit:r,filter:[...m,"role,cs,user",`club_id,eq,${n==null?void 0:n.id}`],join:["user|user_id"]},"PAGINATE");i&&f(!1);const{list:o,limit:Y,num_pages:L,page:b}=i;$(o),j(Y),V(L),k(b),D(b>1),R(b+1<=L)}catch(i){f(!1),console.log("ERROR",i),se(h,i.message)}}const q=()=>{const s=[];t.firstName&&s.push(`${l._project_id}_user.first_name,cs,${t.firstName}`),t.lastName&&s.push(`${l._project_id}_user.last_name,cs,${t.lastName}`),t.email&&s.push(`${l._project_id}_user.email,cs,${t.email}`),t.phone&&s.push(`${l._project_id}_user.phone,cs,${t.phone}`),t.ageGroup&&t.ageGroup!==""&&s.push(`${l._project_id}_age_group,cs,${t.ageGroup}`),t.status!==""&&s.push(`${l._project_id}_status,eq,${t.status}`),t.ntrp&&s.push(`ntrp,cs,${t.ntrp}`),t.membership&&s.push(`slug,cs,${t.membership}`),c(1,d,{},s)};a.useEffect(()=>{n!=null&&n.id&&c(1,d)},[n==null?void 0:n.id]),a.useEffect(()=>{H({type:"SETPATH",payload:{path:"users"}})},[]);const w=s=>{C.current&&!C.current.contains(s.target)&&setOpenFilter(!1)};a.useEffect(()=>(document.addEventListener("mousedown",w),()=>{document.removeEventListener("mousedown",w)}),[]);const J={name:s=>{var r,p,m,i,o;return e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((r=s.user)==null?void 0:r.photo)||"/default-avatar.png",alt:`${(p=s.user)==null?void 0:p.first_name} ${(m=s.user)==null?void 0:m.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("span",{className:"font-medium text-gray-900",children:[P.truncate((i=s.user)==null?void 0:i.first_name,{length:15})," ",P.truncate((o=s.user)==null?void 0:o.last_name,{length:15})]})]})},slug:s=>e.jsx("span",{className:"text-gray-600",children:s.slug||"Member"}),ntrp:s=>e.jsx("span",{className:"text-gray-600",children:s.ntrp}),phone_number:s=>{var r;return e.jsx(de,{phoneNumber:(r=s.user)==null?void 0:r.phone,className:"text-gray-600"})},email:s=>{var r;return e.jsx("span",{className:"text-gray-600",children:(r=s.user)==null?void 0:r.email})},actions:s=>e.jsx("div",{className:"flex items-center justify-end gap-3",children:e.jsx("button",{onClick:r=>{r.stopPropagation(),y(s),_(!0)},className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})})},K=({isOpen:s,onClose:r})=>{const[p,m]=a.useState(!1),i=async()=>{try{m(!0),l.setTable("profile");const o=await l.callRestAPI({id:Number(g.id)},"DELETE");l.setTable("user"),await l.callRestAPI({id:Number(g.user_id)},"DELETE"),await ae(l,{user_id:U,activity_type:M.user_management,action_type:re.DELETE,data:g,club_id:n==null?void 0:n.id,description:"Deleted user"}),o.error||(S(h,"User deleted successfully",3e3,"success"),r(),y(null),c(x,d))}catch(o){S(h,o==null?void 0:o.message,3e3,"error"),console.log(o)}finally{m(!1)}};return e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${s?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Delete user"}),e.jsx("p",{className:"mb-6",children:"Are you sure you want to delete this user?"}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:r,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(te,{onClick:()=>{i()},className:"rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700",loading:p,children:"Yes, delete"})]})]})]})};return e.jsxs("div",{className:"h-full px-8",children:[e.jsx("div",{className:"flex flex-col flex-wrap justify-between gap-4 py-3 md:flex-row md:items-center",children:e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"User Filters"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>G(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]}),e.jsx(oe,{title:"User History",emptyMessage:"No user history found",activityType:M.user_management})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"First Name"}),e.jsx("input",{type:"text",placeholder:"Search by first name",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.firstName,onChange:s=>{u({...t,firstName:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Last Name"}),e.jsx("input",{type:"text",placeholder:"Search by last name",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.lastName,onChange:s=>{u({...t,lastName:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"text",placeholder:"Search by email",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.email,onChange:s=>{u({...t,email:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"text",placeholder:"Search by phone",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.phone,onChange:s=>{u({...t,phone:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Status"}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",value:t.status,onChange:s=>{u({...t,status:s.target.value})},children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"NTRP"}),e.jsx("input",{type:"text",placeholder:"Search by NTRP",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.ntrp,onChange:s=>{u({...t,ntrp:s.target.value.trim()})}})]}),e.jsxs("div",{className:"col-span-full mt-6 flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{u({firstName:"",lastName:"",email:"",phone:"",ageGroup:"",status:"",ntrp:"",membership:""}),c(1,d)},className:"rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Clear Filters"}),e.jsx("button",{onClick:()=>q(),className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Apply Filters"})]})]})]})}),v?e.jsx(ne,{}):e.jsx(e.Fragment,{children:e.jsx(ie,{columns:ce,data:Z,loading:v,renderCustomCell:J,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",emptyMessage:"No users available",loadingMessage:"Loading users...",onClick:s=>E(`/${z}/view-user/${s.id}`)})}),N>0&&e.jsx(le,{currentPage:x,pageCount:N,pageSize:d,canPreviousPage:T,canNextPage:A,updatePageSize:s=>{j(s),c(1,s)},previousPage:O,nextPage:B,gotoPage:s=>c(s,d)}),e.jsx(K,{isOpen:I,onClose:()=>{_(!1),y(null)},selectedUser:g})]})},Ne=me;export{Ne as L};
