import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as o}from"./vendor-851db8c1.js";import{e as y,a1 as w,M as v}from"./index-5b566256.js";let l=new v;function L({setSelectedClub:c,selectedClub:t,setShow:i,onAction:h}){const[m,g]=o.useState([]),[d,f]=o.useState(""),[p,n]=o.useState(!1),x=e=>{c(e)},u=async()=>{n(!0);try{l.setTable("clubs");const e=await l.callRestAPI({},"GETALL");g(e.list)}catch(e){console.error("Error fetching clubs:",e)}finally{n(!1)}},j=async e=>{n(!0);try{const a=await l.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${e.user_id}`,{},"GET");console.log({clubProfileResponse:a}),c(a==null?void 0:a.model),h(a==null?void 0:a.model)}catch(a){console.error("Error fetching data:",a)}finally{n(!1)}};o.useEffect(()=>{u()},[]);const r=m.filter(e=>`${e.name||""}`.toLowerCase().includes(d.toLowerCase()));return s.jsxs("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:[p&&s.jsx(y,{}),s.jsxs("div",{className:"w-96 rounded-lg bg-white shadow-lg",children:[s.jsxs("div",{className:"mb-0 flex items-center justify-between p-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"Select club"}),s.jsx("button",{onClick:()=>setIsCoachModalOpen(!1),className:"text-gray-400 hover:text-gray-600",children:s.jsx(w,{className:"h-5 w-5"})})]}),s.jsx("h2",{className:"px-4 text-base font-medium",children:"Choose club:"}),s.jsxs("div",{className:"p-4",children:[s.jsx("div",{className:"mb-4",children:s.jsxs("div",{className:"flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[s.jsx("span",{className:"w-5",children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),s.jsx("input",{type:"text",placeholder:"search by name",value:d,onChange:e=>f(e.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),s.jsxs("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:[r.length>0&&r.map(e=>s.jsxs("div",{onClick:()=>x(e),className:"flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50",children:[s.jsx("input",{type:"radio",checked:(t==null?void 0:t.id)===(e==null?void 0:e.id),onChange:()=>x(e),className:"h-4 w-4 rounded-full border-gray-300 text-blue-600"}),s.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:s.jsx("img",{src:(e==null?void 0:e.photo)||"/default-avatar.png",alt:`${e.name} avatar`,className:"h-full w-full object-cover"})}),s.jsx("span",{children:e.name})]},e==null?void 0:e.id)),!(r!=null&&r.length)&&s.jsx("p",{className:"text-center text-sm text-gray-500",children:"No clubs found"})]}),s.jsxs("div",{className:"mt-4 flex justify-between gap-3 pt-3",children:[s.jsx("button",{onClick:()=>i(!1),className:"flex-1 rounded-xl border border-gray-300 px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancel"}),s.jsx("button",{onClick:async()=>{t?(await j(t),i(!1)):showToast(globalDispatch,"Please select a member",3e3,"error")},className:"flex-1 rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:"Save and close"})]})]})]})]})}export{L as C};
