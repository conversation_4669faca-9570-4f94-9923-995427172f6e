import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as h,r as x}from"./vendor-851db8c1.js";import{M as g,A as b,G as E,L as r,a6 as w,a7 as A}from"./index-5b566256.js";import{M as c}from"./index-be4468eb.js";import{M as D}from"./index-fcfe1a21.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";new g;const j=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Club Id",accessor:"club_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Level",accessor:"level",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Start Time",accessor:"start_time",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"End Time",accessor:"end_time",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Num Players Needed",accessor:"num_players_needed",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Players",accessor:"players",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Bio",accessor:"bio",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],de=()=>{s.useContext(b),s.useContext(E),h();const[m,t]=s.useState(!1),[o,a]=s.useState(!1),[n,f]=s.useState(),u=x.useRef(null),[v,S]=s.useState([]),d=(i,l,p=[])=>{switch(i){case"add":t(l);break;case"edit":a(l),S(p),f(p[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(D,{columns:j,tableRole:"club",table:"find_a_buddy_requests",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:u})})})}),e.jsx(r,{children:e.jsx(c,{isModalActive:m,closeModalFn:()=>t(!1),children:e.jsx(w,{setSidebar:t})})}),o&&e.jsx(r,{children:e.jsx(c,{isModalActive:o,closeModalFn:()=>a(!1),children:e.jsx(A,{activeId:n,setSidebar:a})})})]})};export{de as default};
