import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as S,r as x}from"./vendor-851db8c1.js";import{M as g,A as b,G as w,L as o,O as E,P as j}from"./index-5b566256.js";import{M as p}from"./index-be4468eb.js";import{M as C}from"./index-fcfe1a21.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";new g;const A=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Clinic Id",accessor:"clinic_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Payment Status",accessor:"payment_status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],le=()=>{s.useContext(b),s.useContext(w),S();const[n,t]=s.useState(!1),[r,i]=s.useState(!1),[m,u]=s.useState(),f=x.useRef(null),[M,h]=s.useState([]),l=(a,d,c=[])=>{switch(a){case"add":t(d);break;case"edit":i(d),h(c),u(c[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(C,{columns:A,tableRole:"club",table:"clinic_bookings",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:a=>l("edit",!0,a)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>l("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:f})})})}),e.jsx(o,{children:e.jsx(p,{isModalActive:n,closeModalFn:()=>t(!1),children:e.jsx(E,{setSidebar:t})})}),r&&e.jsx(o,{children:e.jsx(p,{isModalActive:r,closeModalFn:()=>i(!1),children:e.jsx(j,{activeId:m,setSidebar:i})})})]})};export{le as default};
