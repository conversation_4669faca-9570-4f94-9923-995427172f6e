import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a}from"./vendor-851db8c1.js";import{T as q,G as J,A as K,u as Y,c as Q,J as g,t as W}from"./index-5b566256.js";import{P as X}from"./index-eb1bc208.js";import{_ as Z}from"./lodash-91d5d207.js";import w from"./Skeleton-1e8bf077.js";import{D as ee}from"./DataTable-a2248415.js";let te=new q;const de=({club:j})=>{const{dispatch:S}=a.useContext(J),{dispatch:_,state:d}=a.useContext(K),{club:C}=Y(),n=(d==null?void 0:d.role)||localStorage.getItem("role"),l=j||C,N=(()=>{const e=[{header:"Date",accessor:"create_at"},{header:"Time",accessor:"update_at"},{header:"Staff name",accessor:"user"}];return(n==="admin"||n==="admin_staff"||n==="club"||n==="staff")&&e.push({header:"Club Name/Admin",accessor:"clubs"}),e.push({header:"Action",accessor:"description"}),e})(),[P,E]=a.useState([]),[o,f]=a.useState(10),[A,T]=a.useState(0),[u,D]=a.useState(0),[L,R]=a.useState(!1),[k,F]=a.useState(!1),[x,m]=a.useState(!0),[$]=a.useState(!1),[z]=a.useState(!1),[,B]=a.useState(!1),h=a.useRef(null),O=localStorage.getItem("user");function G(){c(u-1,o)}function H(){c(u+1,o)}async function c(e,s,r=[]){console.log("filters",r),m(!(z||$));try{const i=[];(n==="club"||n==="staff")&&(l!=null&&l.id)&&i.push(`courtmatchup_activity_logs.club_id,eq,${l.id}`);const b=await te.getPaginate("activity_logs",{page:e,limit:s,size:o,filter:[...r,...i],join:["clubs|club_id","user|user_id"]});b&&m(!1);const{list:U,limit:V,num_pages:v,page:p}=b;E(U),f(V),T(v),D(p),R(p>1),F(p+1<=v)}catch(i){m(!1),console.log("ERROR",i),W(_,i.message)}}const I=Z.debounce(e=>{const s=e.target.value.trim(),r=s?[`courtmatchup_activity_logs.description,cs,${s}`]:[];s?c(1,o,r):c(1,o)},500);a.useEffect(()=>{S({type:"SETPATH",payload:{path:"history"}}),(n==="admin"||n==="admin_staff"||(n==="club"||n==="staff")&&(l!=null&&l.id))&&c(1,o)},[n,l==null?void 0:l.id]);const y=e=>{h.current&&!h.current.contains(e.target)&&B(!1)};a.useEffect(()=>(document.addEventListener("mousedown",y),()=>{document.removeEventListener("mousedown",y)}),[]);const M={update_at:e=>t.jsx("span",{className:"text-gray-600",children:new Date(e.update_at).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),user:e=>{const s=(e==null?void 0:e.user)||{};return t.jsx("span",{className:"text-gray-600",children:(s==null?void 0:s.id)==O?"You":`${(s==null?void 0:s.first_name)||"--"} ${(s==null?void 0:s.last_name)||"--"}`})},clubs:e=>{const s=(e==null?void 0:e.clubs)||{};return(e==null?void 0:e.club_id)===0?t.jsx("span",{className:"text-gray-600",children:"Admin"}):t.jsx("span",{className:"text-gray-600",children:(s==null?void 0:s.name)||"--"})},action:e=>t.jsx("span",{className:"text-gray-600",children:(e==null?void 0:e.action)||"--"})};return t.jsxs("div",{className:"h-screen px-8",children:[t.jsxs("div",{className:"flex flex-col !justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsxs("div",{className:"relative flex max-w-md flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(Q,{className:"text-gray-500"})}),t.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search history",onChange:e=>I(e)})]}),t.jsx("div",{className:"flex items-center gap-4",children:t.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:e=>{e.target.value===""?c(1,o):c(1,o,[`courtmatchup_activity_logs.action_type,cs,${e.target.value}`])},children:[t.jsx("option",{value:"",children:"Action type: All"}),t.jsx("option",{value:g.CREATE,children:"Create"}),t.jsx("option",{value:g.UPDATE,children:"Update"}),t.jsx("option",{value:g.DELETE,children:"Delete"})]})})]}),x?t.jsx(w,{}):t.jsx("div",{className:"overflow-x-auto rounded-lg",children:t.jsx(ee,{columns:N,data:P,loading:x,renderCustomCell:M,tableClassName:"min-w-full border-separate border-spacing-y-2",rowClassName:"bg-gray-100 px-4 py-3",cellClassName:"px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",loadingMessage:"Loading..."})}),t.jsx(X,{currentPage:u,pageCount:A,pageSize:o,canPreviousPage:L,canNextPage:k,updatePageSize:e=>{f(e),c(1,e)},previousPage:G,nextPage:H,gotoPage:e=>c(e,o)})]})};export{de as L};
