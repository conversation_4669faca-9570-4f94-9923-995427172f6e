import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as S}from"./vendor-851db8c1.js";import{u as k}from"./react-hook-form-687afde5.js";import{o as E}from"./yup-2824f222.js";import{c as A,a as x}from"./yup-54691517.js";import{G as h,M as P,b,t as D}from"./index-5b566256.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const ce=()=>{var n,l,d,p;const f=A({name:x().required(),description:x().nullable()}).required(),{dispatch:r}=a.useContext(h),{dispatch:g}=a.useContext(h),y=S(),{register:m,handleSubmit:j,setError:N,formState:{errors:o}}=k({resolver:E(f)}),w=async c=>{let v=new P;try{const t=await v.addStripeProduct({name:c.name,description:c.description});if(!t.error)b(r,"Added"),y("/admin/products");else if(t.validation){const u=Object.keys(t.validation);for(let s=0;s<u.length;s++){const i=u[s];console.log(i),N(i,{type:"manual",message:t.validation[i]})}}}catch(t){console.log("Error",t),b(r,t.message),D(r,t.message)}};return a.useEffect(()=>{g({type:"SETPATH",payload:{path:"products"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add a Product"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(w),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...m("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(n=o.name)!=null&&n.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(l=o.name)==null?void 0:l.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),e.jsx("input",{type:"text",placeholder:"Description",...m("description"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(d=o.description)!=null&&d.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(p=o.description)==null?void 0:p.message})]}),e.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ce as default};
