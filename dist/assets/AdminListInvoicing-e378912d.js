import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{r as c,b as e,f as M}from"./vendor-851db8c1.js";import{M as _,e as F,w as O,G as D,A as q}from"./index-5b566256.js";import{c as z,a as m}from"./yup-54691517.js";import"./lodash-91d5d207.js";import{S as H}from"./react-select-c8303602.js";import{I as K}from"./InvoiceList-d4e4e3db.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./index-be4468eb.js";import"./index.esm-3a36c7d6.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./StripeConnectionStatus-c897521c.js";import"./HistoryComponent-4f475aec.js";let h=new _;function U({onSelect:d}){const[g,u]=c.useState([]),[x,r]=c.useState(!1);async function l(){r(!0);try{h.setTable("clubs");const t=await h.callRestAPI({},"GETALL");u(t.list)}catch(t){console.error("Error fetching data:",t)}finally{r(!1)}}const i=async t=>{r(!0);try{const n=await h.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${t.user_id}`,{},"GET");d(n.model)}catch(n){console.error("Error fetching data:",n)}finally{r(!1)}};return c.useEffect(()=>{l()},[]),o.jsxs("div",{children:[x&&o.jsx(F,{}),o.jsxs("div",{className:"mb-4 max-w-xl",children:[o.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),o.jsx(H,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:g.map(t=>({value:t==null?void 0:t.id,label:t.name,user_id:t.user_id})),isMulti:!1,onChange:i})]})]})}let A=new _;const Y=()=>{const{dispatch:d}=e.useContext(D);e.useContext(q),e.useState([]),e.useState(10),e.useState(0),e.useState(0),e.useState(0),e.useState(!1),e.useState(!1);const[g,u]=e.useState(!1),[x,r]=e.useState(!0);e.useState(!1),e.useState(!1),M();const l=e.useRef(null),[i,t]=c.useState(null),[n,P]=c.useState(null);z({id:m(),email:m(),role:m(),status:m()});async function T(a=1,f=10,v={}){r(!0);try{const{invoice_id:p="",firstName:b="",lastName:y="",sportId:w="",receiptId:j="",bookingType:E="",from:I="",until:C="",month:R="",year:L="",sort:N="desc"}=v;console.log("filters",v);const s=new URLSearchParams;b&&s.append("first_name",b),y&&s.append("last_name",y),w&&s.append("sport_id",w),j&&s.append("receipt_id",j),E&&s.append("booking_type",E),I&&s.append("from",I),C&&s.append("until",C),p&&s.append("receipt_id",p),R&&s.append("month",R),L&&s.append("year",L),N&&s.append("sort",N),s.append("club_id",n);const G=await A.callRawAPI(`/v3/api/custom/courtmatchup/admin/billing/invoices?${s.toString()}`,{},"GET");setInvoices(G.invoices)}catch(p){console.error(p)}finally{r(!1)}}const $=async()=>{try{const a=await A.callRawAPI(`/v3/api/custom/courtmatchup/club/profile/${n}`,{},"GET");t(a.model)}catch(a){console.log("ERROR",a)}};e.useEffect(()=>{d({type:"SETPATH",payload:{path:"invoicing"}})},[]);const S=a=>{l.current&&!l.current.contains(a.target)&&u(!1)};e.useEffect(()=>(document.addEventListener("mousedown",S),()=>{document.removeEventListener("mousedown",S)}),[]);const k=async a=>{var f;t(a),await P((f=a.club)==null?void 0:f.id),console.log("club",a),await T()};return console.log({clubId:n}),o.jsxs("div",{className:"h-screen p-8",children:[o.jsx(U,{onSelect:k}),i!=null&&i.id?o.jsx(K,{clubProfile:i,fetchProfile:$,sports:i==null?void 0:i.sports}):o.jsx("div",{className:"flex h-[calc(100vh-200px)] items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:"No Club Selected"}),o.jsx("p",{className:"text-gray-600",children:"Please select a club from the dropdown above to view and manage its details."})]})})]})},Ge=O(Y,"invoicing","You don't have permission to access invoicing management");export{Ge as default};
