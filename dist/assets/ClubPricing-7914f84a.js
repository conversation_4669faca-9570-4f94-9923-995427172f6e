import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as x,f as ve,b as G}from"./vendor-851db8c1.js";import{A as Ne,G as Ce,e as we,v as k,as as Se,R as Pe,K as Te,M as $e,ak as Le,a3 as De,at as _,b as P}from"./index-5b566256.js";import{S as ie}from"./react-select-c8303602.js";import{I as U}from"./InformationCircleIcon-d35f3488.js";let M=new $e;const w=Le(),u=N=>N?N.includes("AM")||N.includes("PM")?N:De(N):"";function Ie({role:N,profileSettings:H,sports:f,fetchSettings:Y,pricing:n,club:I}){var re,ne;const[oe,Z]=x.useState(!1),[ce,ke]=x.useState(!1);ve(),G.useContext(Ne);const{dispatch:C}=G.useContext(Ce),[de,J]=x.useState(!1),[c,K]=x.useState(null),[me,A]=x.useState(!1),[ue,V]=x.useState(!1),[W,xe]=x.useState(null);x.useState("court");const[h,O]=x.useState("all"),pe=localStorage.getItem("role");console.log("pricing",n);const[t,d]=x.useState({sport_id:"",type:"",sub_type:"",is_general:!1,is_sport_general:!1,is_lesson:!1,general_rate:"00.00",lesson_club_fixed_amount:"0.00",lesson_club_percentage:"0",lesson_pricing_type:"fixed",price_by_hours:[{start_time:"",end_time:"",rate:"00.00"}]}),[_e,q]=x.useState([]),[ge,Q]=x.useState([]),[fe,X]=x.useState(w),[y,F]=x.useState({hasConflict:!1,message:"",conflictDetails:null});x.useEffect(()=>{if(t.sport_id){const s=f==null?void 0:f.find(r=>r.id.toString()===t.sport_id);if(s){const r=s.sport_types.filter(a=>a.type).map(a=>({id:a.club_sport_type_id,label:a.type}));q(r),d(a=>({...a,type:"",sub_type:""}))}}else q([])},[t.sport_id,f]),x.useEffect(()=>{if(t.sport_id&&t.type){const s=f==null?void 0:f.find(r=>r.id.toString()===t.sport_id);if(s){const r=s.sport_types.find(a=>a.type===t.type);if(r){const a=r.subtype.map((i,m)=>({id:m+1,name:i}));Q(a),d(i=>({...i,sub_type:""}))}}}else Q([])},[t.sport_id,t.type,f]),x.useEffect(()=>{},[f]);const ee=s=>{if(!s)return w;const r=w.findIndex(a=>a.value===s);return r===-1?w:w.filter((a,i)=>i>r)},B=(s,r=null)=>{let a;if(r!==null){const i=[...t.price_by_hours];if(s.target.name==="start_time"||s.target.name==="end_time"){const m=s.target.value;if(i[r]={...i[r],[s.target.name]:m},s.target.name==="start_time"){const l=ee(m);X(l)}}else i[r]={...i[r],[s.target.name]:s.target.value};a={...t,price_by_hours:i}}else a={...t,[s.target.name]:s.target.value},["sport_id","type","sub_type"].includes(s.target.name)&&(F({hasConflict:!1,message:"",conflictDetails:null}),setTimeout(()=>{E(a)},100));d(a),r!==null&&(s.target.name==="start_time"||s.target.name==="end_time")&&setTimeout(()=>{E(a)},100)},E=s=>{if(!s.is_general&&s.sport_id&&s.type&&s.sub_type){const r=parseInt(s.sport_id),a=s.type,i=s.sub_type,m=n==null?void 0:n.filter(l=>(l.is_general===!1||l.is_general===0)&&l.sport_id===r&&l.type===a&&(l.subtype===i||l.sub_type===i)&&(!c||l.id!==c.id));for(const l of s.price_by_hours){if(!l.start_time||!l.end_time)continue;const p=new Date(`2000/01/01 ${_(l.start_time)}`),o=new Date(`2000/01/01 ${_(l.end_time)}`);if(!(p>=o)){for(const b of m||[])if(b.price_by_hours){const v=JSON.parse(b.price_by_hours);for(const j of v)if(j.start_time&&j.end_time){const S=new Date(`2000/01/01 ${_(j.start_time)}`),g=new Date(`2000/01/01 ${_(j.end_time)}`);if(p<g&&o>S){const T=u(l.start_time),$=u(l.end_time),L=u(j.start_time),D=u(j.end_time);return F({hasConflict:!0,message:`Time slot (${T} - ${$}) overlaps with existing pricing (${L} - ${D}) at rate $${j.rate}/hour for the selected court.`,conflictDetails:{currentSlot:{start:T,end:$,rate:l.rate},existingSlot:{start:L,end:D,rate:j.rate}}}),!0}}}}}}return F({hasConflict:!1,message:"",conflictDetails:null}),!1},se=(s,r,a)=>{const i=[...t.price_by_hours];if(i[a]={...i[a],[r]:s?s.label:""},r==="start_time"&&s){const l=ee(s.value);X(l)}const m={...t,price_by_hours:i};d(m),setTimeout(()=>{E(m)},100)},he=()=>{d({...t,price_by_hours:[...t.price_by_hours,{start_time:"",end_time:"",rate:"00.00"}]})},ye=s=>{K(s);const a=(s.price_by_hours?JSON.parse(s.price_by_hours):[{start_time:"",end_time:"",rate:"00.00"}]).map(o=>({...o,start_time:o.start_time?u(o.start_time):"",end_time:o.end_time?u(o.end_time):""})),i=s.is_general===!0||s.is_general===1,m=s.is_lesson===!0||s.is_lesson===1,l=i&&s.sport_id,p={sport_id:s.sport_id?s.sport_id.toString():"",type:s.type?s.type.toString():"",sub_type:s.subtype?s.subtype.toString():s.sub_type?s.sub_type.toString():"",is_general:i,is_sport_general:l,is_lesson:m,general_rate:s.general_rate||"00.00",lesson_club_fixed_amount:s.lesson_club_fixed_amount||"0.00",lesson_club_percentage:s.lesson_club_percentage||"0",lesson_pricing_type:s.lesson_pricing_type||"fixed",price_by_hours:a};d(p),Z(!0),setTimeout(()=>{E(p)},300)},be=()=>{d({sport_id:"",type:"",sub_type:"",is_general:!1,is_sport_general:!1,is_lesson:!1,general_rate:"00.00",lesson_club_fixed_amount:"0.00",lesson_club_percentage:"0",lesson_pricing_type:"fixed",price_by_hours:[{start_time:"",end_time:"",rate:"00.00"}]}),K(null),F({hasConflict:!1,message:"",conflictDetails:null})},te=()=>{Z(!1),be()},je=()=>{if(t.is_lesson){const s=parseInt(t.sport_id),r=t.type||"",a=t.sub_type||"";if(n==null?void 0:n.find(l=>(l.is_lesson===!0||l.is_lesson===1)&&l.sport_id===s&&(l.type||"")===r&&((l.subtype||"")===a||(l.sub_type||"")===a)&&(!c||l.id!==c.id)))return{hasConflict:!0,message:`Lesson pricing for this sport (${r||"all types"}, ${a||"all sub-types"}) combination already exists. Please edit the existing lesson pricing instead.`};if(n==null?void 0:n.find(l=>(l.is_lesson===!1||l.is_lesson===0||!l.is_lesson)&&(l.is_general===!1||l.is_general===0)&&l.sport_id===s&&(l.type||"")===r&&((l.subtype||"")===a||(l.sub_type||"")===a)&&(!c||l.id!==c.id)))return{hasConflict:!0,message:`Court pricing for this sport (${r||"all types"}, ${a||"all sub-types"}) combination already exists. You can have either court pricing or lesson pricing, but not both for the same combination.`}}if(t.is_general){if(t.is_sport_general){if(!t.sport_id)return{hasConflict:!0,message:"Please select a sport for the sport-specific general price."};const s=parseInt(t.sport_id);if(n==null?void 0:n.find(a=>(a.is_general===!0||a.is_general===1)&&a.sport_id===s&&(!c||a.id!==c.id)))return{hasConflict:!0,message:"A general price for this sport already exists. Please edit the existing general price instead of creating a new one."}}else if(n==null?void 0:n.find(r=>(r.is_general===!0||r.is_general===1)&&(!r.sport_id||r.sport_id===0||r.sport_id==="0")&&(!c||r.id!==c.id)))return{hasConflict:!0,message:"A general price for all sports already exists. Only one general price for all sports is allowed."}}else{const s=parseInt(t.sport_id),r=t.type||"",a=t.sub_type||"";if(n==null?void 0:n.find(l=>(l.is_general===!1||l.is_general===0)&&(l.is_lesson===!1||l.is_lesson===0||!l.is_lesson)&&l.sport_id===s&&l.type===r&&(l.subtype===a||l.sub_type===a)&&(!c||l.id!==c.id)))return{hasConflict:!0,message:"A court price for this sport, type, and sub-type combination already exists. Please edit the existing price instead."};if(n==null?void 0:n.find(l=>(l.is_lesson===!0||l.is_lesson===1)&&l.sport_id===s&&(l.type||"")===r&&((l.subtype||"")===a||(l.sub_type||"")===a)&&(!c||l.id!==c.id)))return{hasConflict:!0,message:`Lesson pricing for this sport (${r||"all types"}, ${a||"all sub-types"}) combination already exists. You can have either court pricing or lesson pricing, but not both for the same combination.`};if(t.price_by_hours.length>0){const l=t.price_by_hours.filter(o=>o.start_time&&o.end_time);for(const o of l){const b=new Date(`2000/01/01 ${_(o.start_time)}`),v=new Date(`2000/01/01 ${_(o.end_time)}`);if(b>=v)return{hasConflict:!0,message:`Invalid time range: ${u(o.start_time)} - ${u(o.end_time)}. End time must be after start time.`}}for(let o=0;o<l.length;o++){const b=l[o],v=new Date(`2000/01/01 ${_(b.start_time)}`),j=new Date(`2000/01/01 ${_(b.end_time)}`);for(let S=o+1;S<l.length;S++){const g=l[S],T=new Date(`2000/01/01 ${_(g.start_time)}`),$=new Date(`2000/01/01 ${_(g.end_time)}`);if(v<$&&j>T){const L=u(b.start_time),D=u(b.end_time),R=u(g.start_time),z=u(g.end_time);return{hasConflict:!0,message:`Time slots cannot overlap within the same pricing entry. Your time slot (${L} - ${D}) at rate $${b.rate}/hour overlaps with another time slot (${R} - ${z}) at rate $${g.rate}/hour. Please adjust the time periods.`}}}}const p=n==null?void 0:n.filter(o=>(o.is_general===!1||o.is_general===0)&&o.sport_id===s&&o.type===r&&(o.subtype===a||o.sub_type===a)&&(!c||o.id!==c.id));for(const o of p||[])if(o.price_by_hours){const b=JSON.parse(o.price_by_hours);for(const v of l){const j=new Date(`2000/01/01 ${_(v.start_time)}`),S=new Date(`2000/01/01 ${_(v.end_time)}`);for(const g of b)if(g.start_time&&g.end_time){const T=new Date(`2000/01/01 ${_(g.start_time)}`),$=new Date(`2000/01/01 ${_(g.end_time)}`);if(j<$&&S>T){const L=u(v.start_time),D=u(v.end_time),R=u(g.start_time),z=u(g.end_time);return{hasConflict:!0,message:`Time slots cannot overlap with existing pricing periods for the same sport/type/subtype. Your time slot (${L} - ${D}) at rate $${v.rate}/hour overlaps with an existing time slot (${R} - ${z}) at rate $${g.rate}/hour. Please adjust the time periods or modify the existing pricing.`}}}}}}}return{hasConflict:!1}},le=async s=>{var i;if(s&&s.preventDefault(),t.is_lesson){if(!t.sport_id){P(C,"Please select a sport for lesson pricing.",3e3,"warning");return}if((t.lesson_pricing_type==="fixed"||t.lesson_pricing_type==="combination")&&(!t.lesson_club_fixed_amount||parseFloat(t.lesson_club_fixed_amount)<0)){P(C,"Please enter a valid fixed amount for the club.",3e3,"warning");return}if((t.lesson_pricing_type==="percentage"||t.lesson_pricing_type==="combination")&&(!t.lesson_club_percentage||parseFloat(t.lesson_club_percentage)<0||parseFloat(t.lesson_club_percentage)>100)){P(C,"Please enter a valid percentage (0-100) for the club.",3e3,"warning");return}}const{hasConflict:r,message:a}=je();if(r){P(C,a,3e3,"warning");return}if(y.hasConflict){P(C,y.message,3e3,"warning");return}try{J(!0);const l={price_by_hours:t.is_general?[{rate:t.general_rate}]:t.is_lesson?[{rate:"0"}]:t.price_by_hours.map(p=>({...p,start_time:p.start_time?_(p.start_time):"",end_time:p.end_time?_(p.end_time):""})),sport_id:t.is_sport_general?parseInt(t.sport_id):t.is_general?0:parseInt(t.sport_id),subtype:t.sub_type||"",club_id:I==null?void 0:I.id,type:t.type||"",is_general:t.is_general,is_lesson:t.is_lesson,general_rate:t.is_general?t.general_rate:null,lesson_club_fixed_amount:t.is_lesson?t.lesson_club_fixed_amount:null,lesson_club_percentage:t.is_lesson?t.lesson_club_percentage:null,lesson_pricing_type:t.is_lesson?t.lesson_pricing_type:null};c&&(l.club_pricing_id=c.id),await M.callRawAPI(N==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${(i=H==null?void 0:H.user)==null?void 0:i.id}`:`/v3/api/custom/courtmatchup/${pe}/profile-edit`,{pricing:[l]},"POST"),await Y(),P(C,c?"Pricing updated successfully":"Pricing added successfully",3e3,"success"),te()}catch(m){console.log(m)}finally{J(!1)}},ae=async()=>{V(!0);try{N==="admin"?(M.setTable("club_pricing"),await M.callRestAPI({id:W.id},"DELETE")):(M.setTable("club_pricing"),await M.callRestAPI({id:W.id},"DELETE")),P(C,"Pricing deleted successfully",3e3,"success"),Y(),A(!1)}catch(s){console.log(s),V(!1)}finally{V(!1)}};return G.useEffect(()=>{C({type:"SETPATH",payload:{path:"club-ui"}})},[]),e.jsxs("div",{className:"flex flex-col gap-4 p-5 ",children:[ce&&e.jsx(we,{}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xl font-medium",children:"Pricing"}),e.jsxs("button",{onClick:()=>Z(!0),className:"flex items-center gap-2 rounded-xl bg-primaryBlue px-4 py-2 text-white",children:[e.jsx("span",{children:"Add new"}),e.jsx("span",{className:"text-xl",children:"+"})]})]}),e.jsxs("div",{className:"flex gap-1 rounded-lg bg-gray-100 p-1",children:[e.jsx("button",{onClick:()=>O("all"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${h==="all"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"All Pricing"}),e.jsx("button",{onClick:()=>O("court"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${h==="court"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Court Pricing"}),e.jsx("button",{onClick:()=>O("lesson"),className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${h==="lesson"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"Lesson Pricing"})]}),e.jsx("div",{className:"relative overflow-x-auto",children:e.jsxs("div",{className:"w-full min-w-[1000px]",children:[h!=="all"&&e.jsx("div",{className:"mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-5 w-5 text-blue-600",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:h==="lesson"?"Showing lesson pricing only - These define the club's revenue share from lessons":"Showing court pricing only - These define hourly rates for court bookings"})]})}),e.jsxs("table",{className:"w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"pb-4",children:"Price"}),e.jsx("th",{className:"pb-4",children:"Sport"}),e.jsx("th",{className:"pb-4",children:"Type"}),e.jsx("th",{className:"pb-4",children:"Sub-type"}),e.jsx("th",{className:"pb-4",children:"Category"}),e.jsx("th",{className:"pb-4"})]})}),e.jsxs("tbody",{children:[(n==null?void 0:n.length)>0&&((re=n==null?void 0:n.filter(s=>h==="all"?!0:h==="lesson"?s.is_lesson:h==="court"?!s.is_lesson:!0))==null?void 0:re.map(s=>{var r,a;return e.jsxs("tr",{className:"overflow-hidden bg-white",children:[e.jsx("td",{className:"rounded-l-xl bg-white px-4 py-3 text-gray-600",children:s.is_lesson?e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsxs("div",{className:"flex items-center gap-1 font-medium text-blue-600",children:[e.jsx("span",{children:"Lesson Pricing"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This defines the club's revenue share from lessons for this sport/type/subtype combination."})]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[s.lesson_pricing_type==="fixed"&&e.jsxs("span",{children:["Fixed:"," ",k(s.lesson_club_fixed_amount)]}),s.lesson_pricing_type==="percentage"&&e.jsxs("span",{children:["Percentage: ",s.lesson_club_percentage,"% of coach rate"]}),s.lesson_pricing_type==="combination"&&e.jsxs("span",{children:["Fixed:"," ",k(s.lesson_club_fixed_amount)," +"," ",s.lesson_club_percentage,"% of coach rate"]})]})]}):s.is_general?e.jsxs("div",{className:"flex items-center gap-1 font-medium",children:[e.jsx("span",{children:s.sport_id&&s.sport_id!==0?`Sport general price: ${k(s.general_rate)}/hour`:`Global general price: ${k(s.general_rate)}/hour`}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:s.sport_id&&s.sport_id!==0?"This price will be applied to all types and sub-types of this sport for all times of day that are not otherwise defined.":"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]}):s.price_by_hours&&JSON.parse(s.price_by_hours).map((i,m)=>e.jsx("div",{children:i.start_time&&i.end_time?e.jsxs(e.Fragment,{children:[u(i.start_time)," -"," ",u(i.end_time)," :"," ",k(i.rate)]}):e.jsx(e.Fragment,{children:k(i.rate)})},m))}),e.jsx("td",{className:"bg-white px-4 py-3",children:((r=f.find(i=>i.id==s.sport_id))==null?void 0:r.name)||"--"}),e.jsx("td",{className:"bg-white px-4 py-3",children:s.is_lesson&&(!s.type||s.type==="")?e.jsx("span",{className:"italic text-gray-500",children:"All types"}):((a=Se.find(i=>i.label==s.type))==null?void 0:a.label)||s.type||"--"}),e.jsx("td",{className:"bg-white px-4 py-3",children:s.is_lesson&&!s.subtype&&!s.sub_type?e.jsx("span",{className:"italic text-gray-500",children:"All sub-types"}):s.subtype||s.sub_type||"--"}),e.jsx("td",{className:"bg-white px-4 py-3",children:s.is_lesson?e.jsx("span",{className:"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800",children:"Lesson"}):s.is_general?e.jsx("span",{className:"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800",children:"General"}):e.jsx("span",{className:"inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800",children:"Court"})}),e.jsxs("td",{className:"flex items-center gap-2 rounded-r-xl bg-white px-4 py-3",children:[e.jsx("button",{className:"flex items-center justify-center",onClick:()=>ye(s),children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.2422 5.25L18.75 7.75781M13.3652 18.121L18.7473 12.74C19.2378 12.2495 19.5125 11.5875 19.5125 10.8973C19.5125 10.2071 19.2378 9.54511 18.7473 9.05469L14.9566 5.26406C14.4662 4.77356 13.8042 4.49883 13.114 4.49883C12.4238 4.49883 11.7618 4.77356 11.2714 5.26406L5.88925 10.6462C5.64744 10.888 5.5 11.2091 5.5 11.5434V16.9255C5.5 17.6334 6.07284 18.2062 6.78075 18.2062H12.163C12.4972 18.2062 12.8184 18.0588 13.0602 17.817L13.3652 18.1221L13.3652 18.1221Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{onClick:()=>{A(!0),xe(s)},className:"flex items-center justify-center",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.68964 18.3144L2.94119 18.3627L3.68964 18.3144ZM16.3104 18.3144L17.0588 18.3627V18.3627L16.3104 18.3144ZM0.75 3C0.335786 3 0 3.33579 0 3.75C0 4.16421 0.335786 4.5 0.75 4.5V3ZM19.25 4.5C19.6642 4.5 20 4.16421 20 3.75C20 3.33579 19.6642 3 19.25 3V4.5ZM8.5 8.75C8.5 8.33579 8.16421 8 7.75 8C7.33579 8 7 8.33579 7 8.75H8.5ZM7 14.25C7 14.6642 7.33579 15 7.75 15C8.16421 15 8.5 14.6642 8.5 14.25H7ZM13 8.75C13 8.33579 12.6642 8 12.25 8C11.8358 8 11.5 8.33579 11.5 8.75H13ZM11.5 14.25C11.5 14.6642 11.8358 15 12.25 15C12.6642 15 13 14.6642 13 14.25H11.5ZM13.1477 3.93694C13.2509 4.33808 13.6598 4.57957 14.0609 4.47633C14.4621 4.37308 14.7036 3.9642 14.6003 3.56306L13.1477 3.93694ZM2.00156 3.79829L2.94119 18.3627L4.43808 18.2661L3.49844 3.70171L2.00156 3.79829ZM4.68756 20H15.3124V18.5H4.68756V20ZM17.0588 18.3627L17.9984 3.79829L16.5016 3.70171L15.5619 18.2661L17.0588 18.3627ZM17.25 3H2.75V4.5H17.25V3ZM0.75 4.5H2.75V3H0.75V4.5ZM17.25 4.5H19.25V3H17.25V4.5ZM15.3124 20C16.2352 20 16.9994 19.2835 17.0588 18.3627L15.5619 18.2661C15.5534 18.3976 15.4443 18.5 15.3124 18.5V20ZM2.94119 18.3627C3.0006 19.2835 3.76481 20 4.68756 20V18.5C4.55574 18.5 4.44657 18.3976 4.43808 18.2661L2.94119 18.3627ZM7 8.75V14.25H8.5V8.75H7ZM11.5 8.75V14.25H13V8.75H11.5ZM10 1.5C11.5134 1.5 12.7868 2.53504 13.1477 3.93694L14.6003 3.56306C14.0731 1.51451 12.2144 0 10 0V1.5ZM6.85237 3.93694C7.21319 2.53504 8.48668 1.5 10 1.5V0C7.78568 0 5.92697 1.51451 5.39971 3.56306L6.85237 3.93694Z",fill:"#868C98"})})})]})]},s.id)})),(n==null?void 0:n.length)===0&&e.jsx("tr",{children:e.jsx("td",{colSpan:"6",className:"text-center",children:"No pricing found"})}),(n==null?void 0:n.length)>0&&((ne=n==null?void 0:n.filter(s=>h==="all"?!0:h==="lesson"?s.is_lesson:h==="court"?!s.is_lesson:!0))==null?void 0:ne.length)===0&&e.jsx("tr",{children:e.jsxs("td",{colSpan:"6",className:"py-8 text-center text-gray-500",children:["No ",h," pricing found"]})})]})]})]})}),e.jsx(Pe,{isOpen:oe,onClose:te,title:c?"Edit Pricing":"Add Pricing",onPrimaryAction:le,submitting:de,primaryButtonText:c?"Update":"Save",primaryButtonDisabled:y.hasConflict,children:e.jsxs("form",{onSubmit:le,className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm",children:"Sport"}),e.jsxs("select",{name:"sport_id",value:t.sport_id,onChange:B,className:"rounded-lg border border-gray-200 px-3 py-2",disabled:t.is_general&&!t.is_sport_general,children:[e.jsx("option",{value:"",children:"- select -"}),f==null?void 0:f.filter(s=>s.status===1).map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),!t.is_general&&!t.is_lesson&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Please manually select a sport. No automatic selection will occur."}),t.is_general&&!t.is_sport_general&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Sport selection is disabled for global general pricing."}),t.is_general&&t.is_sport_general&&!t.sport_id&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please select a sport for sport-specific general pricing."}),t.is_lesson&&!t.sport_id&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please select a sport for lesson pricing configuration."})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("label",{className:"text-sm",children:["Type"," ",t.is_lesson&&e.jsx("span",{className:"text-gray-400",children:"(optional)"})]}),e.jsxs("select",{name:"type",value:t.type,onChange:B,className:"rounded-lg border border-gray-200 px-3 py-2",disabled:!t.sport_id||t.is_general,children:[e.jsx("option",{value:"",children:"- select -"}),_e.map(s=>e.jsx("option",{value:s.label,children:s.label},s.id))]}),!t.is_general&&!t.is_lesson&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Please manually select a type. No automatic selection will occur."}),t.is_general&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Type selection is disabled for general pricing."}),t.is_lesson&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Type is optional for lesson pricing. Leave blank to apply to all types."})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("label",{className:"text-sm",children:["Sub-type"," ",t.is_lesson&&e.jsx("span",{className:"text-gray-400",children:"(optional)"})]}),e.jsxs("select",{name:"sub_type",value:t.sub_type,onChange:B,className:"rounded-lg border border-gray-200 px-3 py-2",disabled:(!t.type||t.is_general)&&!t.is_lesson,children:[e.jsx("option",{value:"",children:"- select -"}),ge.map(s=>e.jsx("option",{value:s.name,children:s.name},s.id))]}),!t.is_general&&!t.is_lesson&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Please manually select a sub-type. No automatic selection will occur."}),t.is_general&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Sub-type selection is disabled for general pricing."}),t.is_lesson&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Sub-type is optional for lesson pricing. Leave blank to apply to all sub-types."})]}),e.jsxs("div",{className:"mt-2 flex flex-col gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",id:"is_general",name:"is_general",checked:t.is_general,onChange:s=>{d({...t,is_general:s.target.checked,is_sport_general:s.target.checked?t.is_sport_general:!1})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("div",{className:"flex items-center gap-1",children:e.jsx("label",{htmlFor:"is_general",className:"text-sm font-medium",children:"General price"})})]}),t.is_general&&e.jsxs("div",{className:"ml-6 flex flex-col gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",id:"global_general",name:"general_type",checked:!t.is_sport_general,onChange:()=>{d({...t,is_sport_general:!1,sport_id:""})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("label",{htmlFor:"global_general",className:"text-sm font-medium",children:"General price for all sports"}),e.jsxs("div",{className:"group relative",children:[e.jsx(U,{className:"h-4 w-4 text-black"}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",id:"sport_general",name:"general_type",checked:t.is_sport_general,onChange:()=>{d({...t,is_sport_general:!0})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("label",{htmlFor:"sport_general",className:"text-sm font-medium",children:"General price for selected sport"}),e.jsxs("div",{className:"group relative",children:[e.jsx(U,{className:"h-4 w-4 text-black"}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all types and sub-types of the selected sport for all times of day that are not otherwise defined."})]})]})]})]})]}),e.jsxs("div",{className:"mt-2 flex flex-col gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",id:"is_lesson",name:"is_lesson",checked:t.is_lesson,onChange:s=>{d({...t,is_lesson:s.target.checked,lesson_club_fixed_amount:s.target.checked?t.lesson_club_fixed_amount:"0.00",lesson_club_percentage:s.target.checked?t.lesson_club_percentage:"0",lesson_pricing_type:s.target.checked?t.lesson_pricing_type:"fixed"})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("label",{htmlFor:"is_lesson",className:"text-sm font-medium",children:"Lesson"}),e.jsxs("div",{className:"group relative",children:[e.jsx(U,{className:"h-4 w-4 text-black"}),e.jsx("div",{className:"absolute left-full top-1/2 z-10 ml-2 w-64 -translate-y-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"Define the club's share of revenue from lessons for this sport/type/subtype combination."})]})]})]}),t.is_lesson&&e.jsxs("div",{className:"ml-6 flex flex-col gap-3 rounded-lg border border-blue-200 bg-blue-50 p-4",children:[e.jsx("div",{className:"text-sm font-medium text-blue-800",children:"Club Revenue Share Configuration"}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",id:"lesson_fixed",name:"lesson_pricing_type",value:"fixed",checked:t.lesson_pricing_type==="fixed",onChange:s=>{d({...t,lesson_pricing_type:s.target.value})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("label",{htmlFor:"lesson_fixed",className:"text-sm font-medium",children:"Fixed amount for club"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",id:"lesson_percentage",name:"lesson_pricing_type",value:"percentage",checked:t.lesson_pricing_type==="percentage",onChange:s=>{d({...t,lesson_pricing_type:s.target.value})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("label",{htmlFor:"lesson_percentage",className:"text-sm font-medium",children:"Percentage of coach's rate"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"radio",id:"lesson_combination",name:"lesson_pricing_type",value:"combination",checked:t.lesson_pricing_type==="combination",onChange:s=>{d({...t,lesson_pricing_type:s.target.value})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("label",{htmlFor:"lesson_combination",className:"text-sm font-medium",children:"Combination of both"})]})]}),(t.lesson_pricing_type==="fixed"||t.lesson_pricing_type==="combination")&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Fixed Amount for Club"}),e.jsxs("div",{className:"flex rounded-lg border border-gray-200",children:[e.jsx("span",{className:"flex items-center border-r border-gray-200 px-3 text-sm text-gray-500",children:"USD"}),e.jsx("input",{type:"number",step:"0.01",min:"0",name:"lesson_club_fixed_amount",value:t.lesson_club_fixed_amount,onChange:s=>{d({...t,lesson_club_fixed_amount:s.target.value})},className:"flex-1 border-gray-200 px-3 py-2",placeholder:"0.00"})]})]}),(t.lesson_pricing_type==="percentage"||t.lesson_pricing_type==="combination")&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Percentage of Coach Rate"}),e.jsxs("div",{className:"flex rounded-lg border border-gray-200",children:[e.jsx("input",{type:"number",step:"1",min:"0",max:"100",name:"lesson_club_percentage",value:t.lesson_club_percentage,onChange:s=>{d({...t,lesson_club_percentage:s.target.value})},className:"flex-1 border-gray-200 px-3 py-2",placeholder:"0"}),e.jsx("span",{className:"flex items-center border-l border-gray-200 px-3 text-sm text-gray-500",children:"%"})]})]})]})]}),t.is_general?e.jsxs("div",{className:"flex flex-col gap-2 rounded-lg border border-gray-200 p-4",children:[e.jsx("label",{className:"text-sm font-medium",children:"General Price per Hour"}),e.jsxs("div",{className:"flex rounded-lg border border-gray-200",children:[e.jsx("span",{className:"flex items-center border-r border-gray-200 px-3 text-sm text-gray-500",children:"USD/h"}),e.jsx("input",{type:"number",name:"general_rate",value:t.general_rate,onChange:s=>{d({...t,general_rate:s.target.value})},className:"flex-1 border-gray-200 px-3 py-2"})]})]}):t.is_lesson?e.jsx("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4",children:e.jsxs("div",{className:"text-sm text-blue-800",children:[e.jsx("p",{className:"font-medium",children:"Lesson pricing configured"}),e.jsx("p",{className:"mt-1 text-xs",children:"Time-based pricing is not applicable for lessons. The club's revenue share will be calculated based on the configuration above."})]})}):e.jsxs(e.Fragment,{children:[t.price_by_hours.map((s,r)=>e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsx("div",{className:"flex flex-1 flex-col gap-2",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"text-sm",children:"From"}),e.jsx(ie,{options:w,className:"w-full rounded-lg text-sm",placeholder:"Select start time",value:s.start_time?w.find(a=>a.label===s.start_time):null,onChange:a=>se(a,"start_time",r)})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"text-sm",children:"Until"}),e.jsx(ie,{options:fe,className:"w-full rounded-lg text-sm",placeholder:s.start_time?"Select end time":"Select start time first",value:s.end_time?w.find(a=>a.label===s.end_time):null,onChange:a=>se(a,"end_time",r),isDisabled:!s.start_time})]})]})}),e.jsx("button",{type:"button",onClick:()=>{const a=[...t.price_by_hours];a.splice(r,1),d({...t,price_by_hours:a})},className:"p-1 text-red-500 hover:text-red-700",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"h-5 w-5",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})})})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm",children:"Price"}),e.jsxs("div",{className:"flex rounded-lg border border-gray-200",children:[e.jsx("span",{className:"flex items-center border-r border-gray-200 px-3 text-sm text-gray-500",children:"USD/h"}),e.jsx("input",{type:"number",name:"rate",value:s.rate,onChange:a=>B(a,r),className:"flex-1 border-gray-200 px-3 py-2"})]})]})]},r)),y.hasConflict&&e.jsx("div",{className:"mt-4 rounded-lg border border-red-200 bg-red-50 p-4",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-red-600",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Pricing Conflict Detected"}),e.jsxs("div",{className:"mt-2 text-sm text-red-700",children:[e.jsx("p",{children:y.message}),y.conflictDetails&&e.jsxs("div",{className:"mt-2 rounded border border-red-300 bg-red-100 p-2",children:[e.jsx("p",{className:"font-medium",children:"Conflict Details:"}),e.jsxs("p",{children:["Your selection:"," ",y.conflictDetails.currentSlot.start," ","-"," ",y.conflictDetails.currentSlot.end," ","at $",y.conflictDetails.currentSlot.rate,"/hour"]}),e.jsxs("p",{children:["Existing pricing:"," ",y.conflictDetails.existingSlot.start," ","-"," ",y.conflictDetails.existingSlot.end," ","at $",y.conflictDetails.existingSlot.rate,"/hour"]})]})]}),e.jsx("p",{className:"mt-2 text-sm font-semibold text-red-800",children:"The Save button is disabled. Please adjust your time selection to avoid overlapping with existing pricing."})]})]})}),e.jsx("button",{type:"button",onClick:he,className:"mt-2 max-w-fit rounded-lg border border-gray-300 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"+ Add Price Period"})]})]})}),e.jsx(Te,{isOpen:me,onClose:()=>A(!1),onConfirm:ae,title:"Delete Pricing",message:"Are you sure you want to delete this pricing?",loading:ue,onDelete:ae})]})}export{Ie as C};
