import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as o,f as A,r as n,j as R}from"./vendor-851db8c1.js";import{u as D}from"./react-hook-form-687afde5.js";import{o as U}from"./yup-2824f222.js";import{c as B,a as f}from"./yup-54691517.js";import{M as F,A as L,G as M,t as G,d as O,b as H}from"./index-5b566256.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as b}from"./MkdInput-829a7fd3.js";import{S as $}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let m=new F;const Ut=r=>{const{dispatch:I}=o.useContext(L),j=B({clinic_id:f(),user_id:f(),status:f(),payment_status:f()}).required(),{dispatch:g}=o.useContext(M),[h,q]=o.useState({}),[x,y]=o.useState(!1),[E,_]=o.useState(!1),k=A(),[K,v]=n.useState(0),[V,w]=n.useState(0),[z,N]=n.useState(""),[J,C]=n.useState(""),{register:c,handleSubmit:P,setError:S,setValue:d,formState:{errors:u}}=D({resolver:U(j)}),i=R();n.useEffect(function(){(async function(){try{_(!0),m.setTable("clinic_bookings");const t=await m.callRestAPI({id:r.activeId?r.activeId:Number(i==null?void 0:i.id)},"GET");t.error||(d("clinic_id",t.model.clinic_id),d("user_id",t.model.user_id),d("status",t.model.status),d("payment_status",t.model.payment_status),v(t.model.clinic_id),w(t.model.user_id),N(t.model.status),C(t.model.payment_status),_(!1))}catch(t){_(!1),console.log("error",t),G(I,t.message)}})()},[]);const T=async t=>{y(!0);try{m.setTable("clinic_bookings");for(let l in h){let a=new FormData;a.append("file",h[l].file);let p=await m.uploadImage(a);t[l]=p.url}const s=await m.callRestAPI({id:r.activeId?r.activeId:Number(i==null?void 0:i.id),clinic_id:t.clinic_id,user_id:t.user_id,status:t.status,payment_status:t.payment_status},"PUT");if(!s.error)H(g,"Updated"),k("/club/clinic_bookings"),g({type:"REFRESH_DATA",payload:{refreshData:!0}}),r.setSidebar(!1);else if(s.validation){const l=Object.keys(s.validation);for(let a=0;a<l.length;a++){const p=l[a];S(p,{type:"manual",message:s.validation[p]})}}y(!1)}catch(s){y(!1),console.log("Error",s),S("clinic_id",{type:"manual",message:s.message})}};return o.useEffect(()=>{g({type:"SETPATH",payload:{path:"clinic_bookings"}})},[]),e.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Edit Clinic Bookings"}),E?e.jsx($,{}):e.jsxs("form",{className:" w-full max-w-lg",onSubmit:P(T),children:[e.jsx(b,{type:"number",page:"edit",name:"clinic_id",errors:u,label:"Clinic Id",placeholder:"Clinic Id",register:c,className:""}),e.jsx(b,{type:"number",page:"edit",name:"user_id",errors:u,label:"User Id",placeholder:"User Id",register:c,className:""}),e.jsx(b,{page:"edit",name:"status",errors:u,label:"Status",placeholder:"Status",register:c,className:""}),e.jsx(b,{page:"edit",name:"payment_status",errors:u,label:"Payment Status",placeholder:"Payment Status",register:c,className:""}),e.jsx(O,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:x,disable:x,children:"Submit"})]})]})};export{Ut as default};
