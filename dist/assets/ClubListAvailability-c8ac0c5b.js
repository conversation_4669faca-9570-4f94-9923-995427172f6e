import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{A as f}from"./Availability-4ecf83c9.js";import{r}from"./vendor-851db8c1.js";import{u as y,G as b,e as v,M as x}from"./index-5b566256.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./date-fns-07266b7d.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let A=new x;function tt(){const[e,n]=r.useState(null),{club:t,sports:l}=y();r.useContext(b);const[u,p]=r.useState(!1);async function s(i={}){try{p(!0);let o=new URLSearchParams;i.date&&o.append("date",i.date),i.time&&o.append("time",i.time),i.sport_id&&o.append("sport_id",i.sport_id);const m=o.toString(),c=`/v3/api/custom/courtmatchup/club/reservations/availability${m?`?${m}`:""}`,d=await A.callRawAPI(c,{},"GET");n(d)}catch(o){console.log(o)}finally{p(!1)}}return r.useEffect(()=>{t!=null&&t.id&&s()},[t==null?void 0:t.id]),a.jsxs("div",{children:[u&&a.jsx(v,{}),a.jsx(f,{clubAvailability:e,fetchClubAvailability:s,sports:l,club:t})]})}export{tt as default};
