import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as K,r as o}from"./vendor-851db8c1.js";import{u as q}from"./react-hook-form-687afde5.js";import{c as ee,a as j}from"./yup-54691517.js";import{o as te}from"./yup-2824f222.js";import{M as se,T as ae,A as re,G as oe,t as u,aJ as M,N as ie,aR as N,H as le,E as ce,J as ne,b as S,aS as de,aT as me}from"./index-5b566256.js";import"./lodash-91d5d207.js";let i=new se;new ae;const ge=ee({answer:j().required("Answer is required"),category:j().required("Category is required"),subcategory_id:j().required("Subcategory is required")});function we({onClose:a,onSuccess:c,club:s}){const{dispatch:d}=K.useContext(re),{dispatch:b}=K.useContext(oe),[C,v]=o.useState(!1),[H,_]=o.useState([]),[V,h]=o.useState([]),[x,p]=o.useState(!1),[A,k]=o.useState(!1),[m,E]=o.useState(""),[y,B]=o.useState(""),[T,F]=o.useState(!1),[I,L]=o.useState(!1),[R,P]=o.useState(!1),[D,Q]=o.useState(!1),{register:f,handleSubmit:z,watch:G,setValue:$,formState:{errors:n}}=q({resolver:te(ge)}),l=G("category"),O=G("subcategory_id");o.useEffect(()=>{U()},[]),o.useEffect(()=>{l?J(l):h([])},[l]);const U=async()=>{P(!0);try{i.setTable("faq_category");const t=await i.callRestAPI({filter:s!=null&&s.id?[`club_id,eq,${s.id}`]:["club_id,eq,0"]},"GETALL");_(t.list||[])}catch(t){console.error("Error fetching categories:",t),u(d,t.message)}finally{P(!1)}},J=async t=>{Q(!0);try{i.setTable("faq_subcategory");const r=await i.callRestAPI({filter:[`category_id,eq,${t}`]},"GETALL");h(r.list||[])}catch(r){console.error("Error fetching subcategories:",r),u(d,r.message)}finally{Q(!1)}},W=async()=>{if(m.trim()){F(!0);try{i.setTable("faq_category");const t={name:m.trim(),club_id:s!=null&&s.id?s.id:0},r=await i.callRestAPI(t,"POST");if(await le(i,{user_id:localStorage.getItem("user"),activity_type:ce.faq,action_type:ne.CREATE,data:{newCategoryName:m.trim()},club_id:0,description:"Admin created a FAQ"}),r&&!r.error){const g=r.data;S(b,"Category created successfully"),E(""),p(!1);const w={id:g,name:m.trim(),club_id:s!=null&&s.id?s.id:0};_(Z=>[...Z,w]),$("category",g.toString()),J(g)}}catch(t){console.error("Error creating category:",t),u(d,t.message)}finally{F(!1)}}},X=async()=>{if(!(!y.trim()||!l)){L(!0);try{i.setTable("faq_subcategory");const t=await i.callRestAPI({name:y.trim(),category_id:l},"POST");if(t&&!t.error){const r=t.data;S(b,"Subcategory created successfully"),B(""),k(!1);const g={id:r,name:y.trim(),category_id:l};h(w=>[...w,g]),$("subcategory_id",r.toString())}}catch(t){console.error("Error creating subcategory:",t),u(d,t.message)}finally{L(!1)}}},Y=async t=>{v(!0);try{i.setTable("faq");const r=await i.callRestAPI({answer:t.answer,subcategory_id:t.subcategory_id,general:s!=null&&s.id?0:1,club_id:s!=null&&s.id?s.id:0},"POST");r&&!r.error&&(S(b,"FAQ added successfully"),c==null||c(),a==null||a())}catch(r){console.error("Error adding FAQ:",r),u(d,r.message)}finally{v(!1)}};return e.jsxs("form",{onSubmit:z(Y),className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),x?e.jsxs("button",{type:"button",onClick:()=>p(!x),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700",children:[e.jsx(ie,{className:"h-4 w-4"}),"Cancel"]}):e.jsxs("button",{type:"button",onClick:()=>p(!x),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700",children:[e.jsx(M,{className:"h-4 w-4"}),"Add New"]})]}),x?e.jsxs("div",{className:"mt-1 flex gap-2",children:[e.jsx("input",{type:"text",value:m,onChange:t=>E(t.target.value),placeholder:"Enter new category name",className:"block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:W,disabled:T,className:"flex items-center gap-2 rounded-md bg-primaryBlue px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:T?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]}):e.jsxs("select",{...f("category"),disabled:R,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue disabled:bg-gray-100",children:[e.jsx("option",{value:"",children:"Select a category"}),R?e.jsx("option",{value:"",disabled:!0,children:"Loading categories..."}):H.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),n.category&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.category.message})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subcategory"}),e.jsxs("button",{type:"button",onClick:()=>k(!A),disabled:!l,className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-blue-700 disabled:opacity-50 disabled:hover:text-primaryBlue",children:[e.jsx(M,{className:"h-4 w-4"}),"Add New"]})]}),A?e.jsxs("div",{className:"mt-1 flex gap-2",children:[e.jsx("input",{type:"text",value:y,onChange:t=>B(t.target.value),placeholder:"Enter new subcategory name",className:"block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:X,disabled:I,className:"flex items-center gap-2 rounded-md bg-primaryBlue px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:I?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]}):e.jsxs("select",{...f("subcategory_id"),disabled:!l||D,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue disabled:bg-gray-100",children:[e.jsx("option",{value:"",children:"Select a subcategory"}),D?e.jsx("option",{value:"",disabled:!0,children:"Loading subcategories..."}):V.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),n.subcategory_id&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.subcategory_id.message})]}),O&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Answer"}),e.jsx("textarea",{...f("answer"),rows:4,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Enter the answer for this subcategory"}),n.answer&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.answer.message})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:a,className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:C||!O,className:"flex items-center gap-2 rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50",children:C?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-4 w-4 animate-spin"}),"Saving..."]}):"Save"})]})]})}const je=({faq:a})=>{var c,s;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Category"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:((c=a==null?void 0:a.faq_category)==null?void 0:c.name)||"--"})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Subcategory"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:((s=a==null?void 0:a.faq_subcategory)==null?void 0:s.name)||"--"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Question"}),e.jsxs("div",{className:"mt-1 flex items-start gap-2",children:[e.jsx(de,{className:"mt-1 h-5 w-5 text-gray-400"}),e.jsx("p",{className:"text-lg text-gray-900",children:(a==null?void 0:a.question)||"--"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Answer"}),e.jsxs("div",{className:"mt-1 flex items-start gap-2",children:[e.jsx(me,{className:"mt-1 h-5 w-5 text-gray-400"}),e.jsx("p",{className:"text-lg text-gray-900",children:(a==null?void 0:a.answer)||"--"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Created At"}),e.jsx("p",{className:"mt-1 text-lg text-gray-900",children:new Date(a==null?void 0:a.create_at).toLocaleDateString()||"--"})]})]})};export{we as A,je as F};
