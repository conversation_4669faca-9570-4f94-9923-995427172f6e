import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as ee,r as n}from"./vendor-851db8c1.js";import{R as Y,a3 as G,aE as U,G as te,A as se,u as ae,ab as ne,e as le,T as re,M as ie,b as oe,t as ce}from"./index-5b566256.js";import{q as de}from"./index.esm-09a3a6b8.js";import{f as K,s as me,n as xe}from"./date-fns-07266b7d.js";import{C as ue}from"./Calendar-9031b5fe.js";import{h as he}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";function V({isOpen:l,onClose:y,clinic:h,fetchCoachesForClinic:x}){const D=ee(),[j,_]=n.useState(!1),[g,w]=n.useState([]),[A,O]=n.useState(!1),t=n.useRef(null),c=n.useRef(null),u=()=>{_(!j)};return n.useEffect(()=>{(async()=>{if(l&&h&&h.coach_ids&&Array.isArray(h.coach_ids)&&h.coach_ids.length>0&&x){const k=JSON.stringify([...h.coach_ids].sort());if(t.current===h.id&&c.current===k)return;O(!0);try{const N=await x(h.coach_ids);w(N),t.current=h.id,c.current=k}catch(N){console.error("Error fetching coaches:",N),w([])}finally{O(!1)}}else l||(w([]),O(!1),t.current=null,c.current=null)})()},[l,h==null?void 0:h.id,JSON.stringify(h==null?void 0:h.coach_ids),x]),h?e.jsx(Y,{isOpen:l,onClose:y,title:h.name,showFooter:!1,primaryButtonText:"Join",showOnlyPrimary:!0,className:"!p-0",children:e.jsxs("div",{className:"flex h-full flex-col pb-4",children:[e.jsxs("div",{className:"flex flex-1 flex-col gap-4 p-5 pb-6",children:[e.jsx("div",{className:"inline-flex items-center gap-2",children:h.slots_remaining>0?e.jsxs("span",{className:"rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-sm text-[#176448]",children:["Slots available: ",h.slots_remaining," (out of"," ",h.max_participants,")"]}):e.jsx("span",{className:"rounded-full border border-red-800 bg-red-50 px-3 py-1 text-sm text-red-800",children:"No slots available"})}),e.jsxs("div",{className:"border-1 space-y-1 rounded-xl border border-gray-200 bg-gray-100 p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DATE & TIME"}),e.jsxs("p",{className:"text-base",children:[new Date(h.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric"})," ","• ",G(h.clinic_start_time)," -"," ",G(h.clinic_end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DETAILS"}),e.jsx("p",{className:"text-base",children:h.details})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"COACHES"}),e.jsx("div",{className:"space-y-3",children:A?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 animate-pulse rounded-full bg-gray-200"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 w-24 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-3 w-32 animate-pulse rounded bg-gray-200"})]})]}):g&&g.length>0?g.map(i=>{var k,N,v,F,E,T;return e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((k=i.user)==null?void 0:k.photo)||"/default-avatar.png",alt:`${(N=i.user)==null?void 0:N.first_name} ${(v=i.user)==null?void 0:v.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-base font-medium",children:[(F=i.user)==null?void 0:F.first_name," ",(E=i.user)==null?void 0:E.last_name]}),((T=i.user)==null?void 0:T.email)&&e.jsx("p",{className:"text-sm text-gray-500",children:i.user.email}),i.bio&&e.jsx("p",{className:"mt-1 line-clamp-2 text-xs text-gray-400",children:i.bio}),i.hourly_rate&&e.jsxs("p",{className:"text-xs font-medium text-green-600",children:["$",i.hourly_rate,"/hour"]})]})]},i.id)}):e.jsx("p",{className:"text-base text-gray-500",children:"No coaches assigned"})})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"text-base",children:["Tennis • Indoors •"," ",h.surface_id===1?"Hard Court":"Clay Court"]})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"NTRP"}),e.jsx("p",{className:"text-base",children:"4.0-5.0"})]})]}),e.jsx("div",{className:"sticky bottom-0 border-t border-gray-200 bg-gray-100 p-4",children:h.slots_remaining>0?e.jsxs("button",{onClick:()=>{D(`/user/clinic-booking/${h.id}`,{state:{clinic:h}})},className:"flex w-full items-center justify-center gap-2 rounded-xl bg-primaryBlue py-3 text-center text-white hover:bg-blue-900",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.75 11.5H9.25C8.0197 11.4995 6.81267 11.8354 5.75941 12.4712C4.70614 13.107 3.8467 14.0186 3.274 15.1075C3.2579 14.9054 3.2499 14.7027 3.25 14.5C3.25 10.3578 6.60775 7 10.75 7V2.875L18.625 9.25L10.75 15.625V11.5ZM9.25 10H12.25V12.481L16.2408 9.25L12.25 6.019V8.5H10.75C9.88769 8.49903 9.03535 8.68436 8.25129 9.04332C7.46724 9.40227 6.76999 9.92637 6.20725 10.5797C7.17574 10.1959 8.20822 9.99919 9.25 10Z",fill:"white"})})}),e.jsx("span",{children:" Join"})]}):e.jsx("div",{className:"rounded-2xl border border-gray-200 bg-white p-4 shadow-sm",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Get notifed"}),e.jsx("div",{className:"flex items-center",children:e.jsx("button",{type:"button",className:`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${j?"bg-blue-600":"bg-gray-200"}`,role:"switch","aria-checked":j,onClick:u,children:e.jsx("span",{"aria-hidden":"true",className:`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${j?"translate-x-5":"translate-x-0"}`})})})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"We will email you when slots for this clinic become available again, e.g. if someone opts-put."})]})})})})]})}):null}function Q({getActiveFiltersCount:l,clearFilters:y,setIsFilterModalOpen:h,availableOnly:x,toggleAvailableSlots:D,sortOrder:j,showSortOptions:_,setShowSortOptions:g,sortClinics:w}){return e.jsxs("div",{className:"mb-4 flex flex-col justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4",children:[e.jsxs("button",{onClick:()=>h(!0),className:"flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-1.5 sm:px-4 sm:py-2",children:[e.jsx(de,{className:"text-blue-600"}),e.jsx("span",{className:"text-sm text-gray-700 sm:text-base",children:"Filter"}),l()>0&&e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs text-white",children:l()})]}),l()>0&&e.jsx("button",{onClick:y,className:"text-sm text-gray-500 hover:underline sm:text-base",children:"Clear all"})]}),e.jsxs("div",{className:"mt-3 flex flex-wrap items-center gap-3 sm:mt-0 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"whitespace-nowrap text-xs text-gray-700 sm:text-sm",children:"Available slot only"}),e.jsx("button",{onClick:D,className:`relative h-5 w-10 rounded-full transition-colors duration-200 ease-in-out sm:h-6 sm:w-12 ${x?"bg-blue-600":"bg-gray-200"}`,children:e.jsx("div",{className:`absolute top-0.5 h-4 w-4 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out sm:h-5 sm:w-5 ${x?"translate-x-5 sm:translate-x-6":"translate-x-0.5 sm:translate-x-1"}`})})]}),e.jsxs("div",{className:"relative border-gray-200 sm:border-l sm:pl-4",children:[e.jsxs("button",{onClick:()=>g(!_),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1.5 text-xs sm:gap-2 sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",j==="desc"?"Latest":"Earliest",")"]}),e.jsx(U,{size:16,className:`text-gray-400 transition-transform duration-200 ${_?"rotate-180":""}`})]}),_&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>w("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${j==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",j==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>w("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${j==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",j==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]})}function X({clinic:l}){return e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:l.name}),l.type==1&&e.jsx("span",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white",children:"REGISTERED"})]}),e.jsxs("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:[new Date(l.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})," - "," ",new Date(l.clinic_end_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})," • ",G(l.clinic_start_time)," -"," ",G(l.clinic_end_time),l.clinic_end_date&&e.jsxs("span",{children:[" ","-"," ",new Date(l.clinic_end_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]})]})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:l.slots_remaining>0?e.jsxs("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:["Slots available: ",l.slots_remaining," (out of"," ",l.max_participants,")"]}):e.jsx("span",{className:"w-fit rounded-full border border-red-800 bg-red-50 px-3 py-1 text-xs text-red-800",children:"No slots available"})})]})})}function fe({programs:l,fetchClinics:y,FiltersContent:h,filters:x,setFilters:D,clearFilters:j,fetchCoachesForClinic:_}){const[g,w]=n.useState(!1),[A,O]=n.useState(!1),[t,c]=n.useState(null),[u,i]=n.useState(l),[k,N]=n.useState(!1);n.useState(!1);const[v,F]=n.useState("desc"),[E,T]=n.useState(!1),[C,R]=n.useState(new Date),H=async p=>{N(!0);const S=K(me(p,{weekStartsOn:0}),"yyyy-MM-dd"),L=K(xe(p,{weekStartsOn:0}),"yyyy-MM-dd");let o=[];o.push(`start_date=${S}`),o.push(`end_date=${L}`);const s=Object.entries(x.days).filter(([d,r])=>r).map(([d])=>d.toLowerCase());s.length>0&&o.push(`weekday=${s.join(",")}`);const f=Object.entries(x.timeOfDay).filter(([d,r])=>r).map(([d])=>d.toLowerCase());f.length>0&&o.push(`times=${f.join(",")}`);const a=o.join("&");await y(a),N(!1)},I=p=>{const S=[...u].sort((L,o)=>{const s=new Date(L.clinic_date+" "+L.clinic_start_time),f=new Date(o.clinic_date+" "+o.clinic_start_time);return p==="asc"?s-f:f-s});i(S),F(p),T(!1)};n.useEffect(()=>{let p=[...l];g&&(p=p.filter(L=>parseInt(L.slots_remaining)>0));const S=p.sort((L,o)=>{const s=new Date(L.clinic_date+" "+L.clinic_start_time),f=new Date(o.clinic_date+" "+o.clinic_start_time);return v==="asc"?s-f:f-s});i(S)},[l,v,g]),n.useEffect(()=>{H(C)},[]);const P=()=>{const p=Object.values(x.days).filter(Boolean).length,S=Object.values(x.timeOfDay).filter(Boolean).length,L=x.price.from||x.price.to?1:0;return p+S+L},z=async()=>{N(!0);let p=[];const S=Object.entries(x.days).filter(([s,f])=>f).map(([s])=>s.toLowerCase());S.length>0&&p.push(`weekday=${S.join(",")}`);const L=Object.entries(x.timeOfDay).filter(([s,f])=>f).map(([s])=>s.toLowerCase());L.length>0&&p.push(`times=${L.join(",")}`);const o=p.join("&");await y(o),O(!1),N(!1)},W=()=>{if(w(!g),g)i(l);else{const p=l.filter(S=>parseInt(S.slots_remaining)>0);i(p)}};return console.log("clinics",u),e.jsxs("div",{className:"mx-auto mt-3 max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:mt-5 sm:p-4",children:[e.jsx(Q,{getActiveFiltersCount:P,clearFilters:j,setIsFilterModalOpen:O,availableOnly:g,toggleAvailableSlots:W,sortOrder:v,showSortOptions:E,setShowSortOptions:T,sortClinics:I}),e.jsx("div",{className:"space-y-3 sm:space-y-4",children:u.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:g?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(P()>0||g)&&e.jsx("button",{onClick:()=>{j(),w(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):u.map(p=>e.jsx("div",{onClick:()=>c(p),children:e.jsx(X,{clinic:p})},p.id))}),e.jsx(Y,{isOpen:A,onClose:()=>O(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:z,className:"bg-gray-100",submitting:k,children:e.jsx(h,{filters:x,setFilters:D})}),e.jsx(V,{isOpen:t!==null,onClose:()=>c(null),clinic:t,fetchCoachesForClinic:_})]})}function pe({programs:l,fetchClinics:y,FiltersContent:h,filters:x,setFilters:D,clearFilters:j,clubProfile:_,fetchCoachesForClinic:g}){const[w,A]=n.useState(!1),[O,t]=n.useState(!1),[c,u]=n.useState(new Date),[i,k]=n.useState(null),[N,v]=n.useState(null),[F,E]=n.useState(l),[T,C]=n.useState(!1);n.useState(!1);const[R,H]=n.useState("desc"),[I,P]=n.useState(!1);console.log("clubProfile",_);const z=a=>{const d=[...F].sort((r,m)=>{const b=new Date(r.clinic_date+" "+r.clinic_start_time),M=new Date(m.clinic_date+" "+m.clinic_start_time);return a==="asc"?b-M:M-b});E(d),H(a),P(!1)};n.useEffect(()=>{let a=[...l];i&&(a=a.filter(r=>{const m=new Date(r.clinic_date);return m.getDate()===i.getDate()&&m.getMonth()===i.getMonth()&&m.getFullYear()===i.getFullYear()})),w&&(a=a.filter(r=>parseInt(r.slots_remaining)>0));const d=a.sort((r,m)=>{const b=new Date(r.clinic_date+" "+r.clinic_start_time),M=new Date(m.clinic_date+" "+m.clinic_start_time);return R==="asc"?b-M:M-b});E(d)},[l,R,i,w]);const W=()=>{const a=Object.values(x.days).filter(Boolean).length,d=Object.values(x.timeOfDay).filter(Boolean).length,r=x.price.from||x.price.to?1:0;return a+d+r},p=async a=>{if(a)try{const d=new Date(c.getFullYear(),c.getMonth(),a,0,0,0);k(d);const r=d.toISOString().split("T")[0];let m=[];m.push(`start_date=${r}`),m.push(`end_date=${r}`);const b=Object.entries(x.days).filter(([$,Z])=>Z).map(([$])=>$.toLowerCase());b.length>0&&m.push(`weekday=${b.join(",")}`);const M=Object.entries(x.timeOfDay).filter(([$,Z])=>Z).map(([$])=>$.toLowerCase());M.length>0&&m.push(`times=${M.join(",")}`);const B=m.join("&");await y(B,r)}catch(d){console.error("Error handling date selection:",d)}},S=async()=>{k(null);const a=Object.entries(x.days).filter(([m,b])=>b).map(([m])=>m),d=Object.entries(x.timeOfDay).filter(([m,b])=>b).map(([m])=>m);let r="";a.length>0&&(r+=a.join(",")),d.length>0&&(r&&(r+=","),r+=d.join(",")),await y(r)},L=async()=>{C(!0);let a=[];if(i){const b=i.toISOString().split("T")[0];a.push(`start_date=${b}`),a.push(`end_date=${b}`)}const d=Object.entries(x.days).filter(([b,M])=>M).map(([b])=>b.toLowerCase());d.length>0&&a.push(`weekday=${d.join(",")}`);const r=Object.entries(x.timeOfDay).filter(([b,M])=>M).map(([b])=>b.toLowerCase());r.length>0&&a.push(`times=${r.join(",")}`);const m=a.join("&");await y(m,i?i.toISOString().split("T")[0]:null),t(!1),C(!1)},o=()=>{if(A(!w),w)E(l);else{const a=l.filter(d=>parseInt(d.slots_remaining)>0);E(a)}},s=()=>{u(new Date(c.setMonth(c.getMonth()-1)))},f=()=>{u(new Date(c.setMonth(c.getMonth()+1)))};return e.jsx("div",{children:e.jsx("div",{className:"mx-auto max-w-6xl p-2 sm:p-4",children:e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8",children:[e.jsxs("div",{className:"h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]",children:[e.jsx(ue,{clinics:l,currentMonth:c,selectedDate:i,onDateClick:p,onPreviousMonth:s,onNextMonth:f,onDateSelect:a=>{a&&(k(a),p(a.getDate()))},daysOff:(()=>{try{return _!=null&&_.days_off?JSON.parse(_.days_off):[]}catch(a){return console.error("Error parsing days_off:",a),[]}})()}),i&&e.jsxs("div",{className:"mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4",children:[e.jsxs("span",{className:"mr-2 text-xs text-gray-600 sm:text-sm",children:["Showing clinics for"," ",i.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),e.jsx("button",{onClick:S,className:"text-xs text-blue-600 hover:underline sm:text-sm",children:"Clear"})]})]}),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-5",children:[e.jsx(Q,{getActiveFiltersCount:W,clearFilters:j,setIsFilterModalOpen:t,availableOnly:w,toggleAvailableSlots:o,sortOrder:R,showSortOptions:I,setShowSortOptions:P,sortClinics:z}),F.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:w?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(W()>0||w)&&e.jsx("button",{onClick:()=>{j(),A(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):F.map(a=>e.jsx("div",{onClick:()=>v(a),children:e.jsx(X,{clinic:a})},a.id))]}),e.jsx(Y,{isOpen:O,onClose:()=>t(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:L,className:"bg-gray-100",submitting:T,children:e.jsx(h,{filters:x,setFilters:D})}),e.jsx(V,{isOpen:N!==null,onClose:()=>v(null),clinic:N,fetchCoachesForClinic:g})]})]})})})}function ge({programs:l,fetchClinics:y,FiltersContent:h,filters:x,setFilters:D,clearFilters:j,fetchCoachesForClinic:_}){const[g,w]=n.useState(!1),[A,O]=n.useState(!1),[t,c]=n.useState(null),[u,i]=n.useState(l),[k,N]=n.useState(!1);n.useState(!1);const[v,F]=n.useState("desc"),[E,T]=n.useState(!1),[C,R]=n.useState(0),H=s=>{const f=[...u].sort((a,d)=>{const r=new Date(a.clinic_date+" "+a.clinic_start_time),m=new Date(d.clinic_date+" "+d.clinic_start_time);return s==="asc"?r-m:m-r});i(f),F(s),T(!1)};n.useEffect(()=>{const s=[...l].sort((f,a)=>{const d=new Date(f.clinic_date+" "+f.clinic_start_time),r=new Date(a.clinic_date+" "+a.clinic_start_time);return v==="asc"?d-r:r-d});i(s)},[l,v]);const I=()=>{const s=Object.values(x.days).filter(Boolean).length,f=Object.values(x.timeOfDay).filter(Boolean).length,a=x.price.from||x.price.to?1:0;return s+f+a},P=async()=>{N(!0);let s=[];s.push(`week=${C}`);const f=Object.entries(x.days).filter(([r,m])=>m).map(([r])=>r.toLowerCase());f.length>0&&s.push(`weekday=${f.join(",")}`);const a=Object.entries(x.timeOfDay).filter(([r,m])=>m).map(([r])=>r.toLowerCase());a.length>0&&s.push(`times=${a.join(",")}`);const d=s.join("&");await y(null,!1,d),O(!1),N(!1)},z=()=>{if(w(!g),g)i(l);else{const s=l.filter(f=>parseInt(f.slots_remaining)>0);i(s)}},[W,p]=n.useState(he()),S=async()=>{if(C>0){const s=C-1;R(s),p(f=>f.clone().subtract(1,"week")),await y(null,!1,`filter=week+${s}`)}},L=async()=>{const s=C+1;R(s),p(f=>f.clone().add(1,"week")),await y(null,!1,`filter=week+${s}`)},o=()=>{const s=W.clone().startOf("week"),f=W.clone().endOf("week"),a=`${s.format("MMM D")} - ${f.format("MMM D")}`;return C===0?`This week (${a})`:C===1?`Next week (${a})`:`${C} weeks from now (${a})`};return n.useEffect(()=>{y(null,!1,"filter=week")},[]),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mx-auto max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:p-4",children:[e.jsx("div",{className:"mx-auto mb-3 mt-3 w-fit max-w-xs rounded-xl bg-white p-1 shadow-sm sm:mb-5 sm:mt-5 sm:max-w-lg",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[e.jsx("button",{onClick:S,disabled:C===0,className:`rounded-xl bg-white p-1 text-gray-600 sm:p-2 ${C===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-center text-sm font-medium sm:text-lg",children:o()}),e.jsx("button",{onClick:L,className:"rounded-xl bg-white p-1 text-gray-600 hover:text-gray-800 sm:p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})}),e.jsx(Q,{getActiveFiltersCount:I,clearFilters:j,setIsFilterModalOpen:O,availableOnly:g,toggleAvailableSlots:z,sortOrder:v,showSortOptions:E,setShowSortOptions:T,sortClinics:H}),e.jsx("div",{className:"mx-auto mt-4 max-w-4xl space-y-3 sm:space-y-4",children:u.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:g?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(I()>0||g)&&e.jsx("button",{onClick:()=>{j(),w(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):u.map(s=>e.jsx("div",{onClick:()=>c(s),children:e.jsx(X,{clinic:s})},s.id))})]}),e.jsx(Y,{isOpen:A,onClose:()=>O(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:P,className:"bg-gray-100",submitting:k,children:e.jsx(h,{filters:x,setFilters:D})}),e.jsx(V,{isOpen:t!==null,onClose:()=>c(null),clinic:t,fetchCoachesForClinic:_})]})}let J=new re,ye=new ie;function q({filters:l,setFilters:y,customFilters:h=[],programs:x=[]}){const[D,j]=n.useState({dayOfWeek:!0,timeOfDay:!0,priceRange:!0,...h.reduce((t,c)=>({...t,[`custom_${c.id}`]:!0}),{})}),_=t=>{j(c=>({...c,[t]:!c[t]}))},g=t=>{y(c=>({...c,days:{...c.days,[t.toLowerCase()]:!c.days[t.toLowerCase()]}}))},w=t=>{y(c=>({...c,timeOfDay:{...c.timeOfDay,[t.toLowerCase()]:!c.timeOfDay[t.toLowerCase()]}}))},A=(t,c)=>{y(u=>({...u,price:{...u.price,[t]:c}}))},O=(t,c)=>{y(u=>{var i,k,N;return{...u,customFilters:{...u.customFilters,[t]:(k=(i=u.customFilters)==null?void 0:i[t])!=null&&k.includes(c)?u.customFilters[t].filter(v=>v!==c):[...((N=u.customFilters)==null?void 0:N[t])||[],c]}}})};return e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>_("dayOfWeek"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Day of week"}),e.jsx(U,{size:20,className:`text-gray-400 transition-transform duration-200 ${D.dayOfWeek?"rotate-180":""}`})]}),D.dayOfWeek&&e.jsx("div",{className:"space-y-3",children:["Weekend","Weekday","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].map(t=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:l.days[t.toLowerCase()],onChange:()=>g(t),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>_("timeOfDay"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Time of the day"}),e.jsx(U,{size:20,className:`text-gray-400 transition-transform duration-200 ${D.timeOfDay?"rotate-180":""}`})]}),D.timeOfDay&&e.jsx("div",{className:"space-y-3",children:["Morning","Afternoon","Evening"].map(t=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:l.timeOfDay[t.toLowerCase()],onChange:()=>w(t),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>_("priceRange"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Price range"}),e.jsx(U,{size:20,className:`text-gray-400 transition-transform duration-200 ${D.priceRange?"rotate-180":""}`})]}),D.priceRange&&e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"From"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:l.price.from,onChange:t=>A("from",t.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"To"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:l.price.to,onChange:t=>A("to",t.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]})]})]}),h.filter(t=>t.enabled).map(t=>{const c=[...new Set(x.map(u=>u[t.key]).filter(u=>u!=null&&u!==""))];return c.length===0?null:e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>_(`custom_${t.id}`),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:t.label}),e.jsx(U,{size:20,className:`text-gray-400 transition-transform duration-200 ${D[`custom_${t.id}`]?"rotate-180":""}`})]}),D[`custom_${t.id}`]&&e.jsx("div",{className:"space-y-3",children:c.map(u=>{var i,k;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:((k=(i=l.customFilters)==null?void 0:i[t.key])==null?void 0:k.includes(u))||!1,onChange:()=>O(t.key,u),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:t.key==="recurring"?u===1?"Yes":"No":u})]},u)})})]},t.id)})]})}function at(){const[l,y]=n.useState(null),[h,x]=n.useState([]),[D,j]=n.useState(null),[_,g]=n.useState([]),[w,A]=n.useState([]),{dispatch:O}=n.useContext(te),{dispatch:t}=n.useContext(se),{user_subscription:c,club_membership:u}=ae(),[i,k]=n.useState([]),[N,v]=n.useState(!1),[F,E]=n.useState([]),T=n.useMemo(()=>!(c!=null&&c.planId)||!(u!=null&&u.length)?null:u.find(o=>o.plan_id===c.planId),[c,u]),[C,R]=n.useState({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),H=[{id:"table",label:"Table"},{id:"calendar",label:"Calendar"},{id:"weekly",label:"Weekly"}],I=localStorage.getItem("user"),P=async()=>{var o;try{const s=await J.getOne("user",I,{}),f=await J.getOne("clubs",s.model.club_id,{}),a=await J.getList("sports",{filter:[`club_id,eq,${s.model.club_id}`]});console.log("sportsResponse",a),x(a.list),j(f.model);let d="table";if((o=f.model)!=null&&o.clinic_description)try{const r=JSON.parse(f.model.clinic_description);r.default_view&&(d=r.default_view),r.custom_filters&&E(r.custom_filters)}catch(r){console.error("Error parsing clinic_description:",r)}y(d)}catch(s){console.error(s),y("table")}},z=async()=>{const o=await J.getList("coach",{join:["user|user_id"]}),s=await J.getList("user",{filter:["role,cs,user"]});g(o.list),A(s.list)},W=async(o,s=!1,f="")=>{var a;v(!0);try{let d="/v3/api/custom/courtmatchup/user/clinics";if(f)d+=`?${f}`;else{let m=[];if(!s){if(o&&!isNaN(new Date(o).getTime())){const B=new Date(o);B.setHours(0,0,0,0);const $=new Date(B);$.setDate($.getDate()+6),$.setHours(23,59,59,999),m.push(`start_date=${B.toISOString().split("T")[0]}`),m.push(`clinic_end_date=${$.toISOString().split("T")[0]}`)}const b=Object.entries(C.days).filter(([B,$])=>$).map(([B])=>B.toLowerCase());b.length>0&&m.push(`weekday=${b.join(",")}`);const M=Object.entries(C.timeOfDay).filter(([B,$])=>$).map(([B])=>B.toLowerCase());M.length>0&&m.push(`times=${M.join(",")}`),C.customFilters&&Object.entries(C.customFilters).forEach(([B,$])=>{$&&$.length>0&&m.push(`${B}=${$.join(",")}`)})}((a=T==null?void 0:T.applicable_sports)==null?void 0:a.length)>0&&m.push(`sport_ids=${T.applicable_sports.join(",")}`),m.length===0&&m.push("week=0"),m.length>0&&(d+=`?${m.join("&")}`)}const r=await ye.callRawAPI(d,{},"GET");!r.error&&(r!=null&&r.programs)&&k(r.programs)}catch(d){console.log(d),oe(O,d.message,"3000","error"),ce(t,d.status)}finally{v(!1)}},p=n.useCallback(async o=>{try{if(!o||!Array.isArray(o)||o.length===0)return[];const s=await ne(O,t,"coach",o,"user|user_id");return s.list&&Array.isArray(s.list)?s.list:[]}catch(s){return console.error("Error fetching coaches for clinic:",s),[]}},[O,t]),S=async()=>{R({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),await W(null,!0)};n.useEffect(()=>{(async()=>(v(!0),await P(),await z(),v(!1),O({type:"SETPATH",payload:{path:"program-clinics"}})))()},[]),n.useEffect(()=>{l&&(async()=>(v(!0),await W(),v(!1)))()},[l]),console.log("programs",i);async function L(o){await S(),y(o)}return e.jsxs(e.Fragment,{children:[(N||!l)&&e.jsx(le,{}),l&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4",children:[e.jsx("h1",{className:"mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Clinics"}),e.jsx("div",{className:"mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm",children:H.map(o=>e.jsx("button",{onClick:()=>L(o.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${l===o.id?"bg-white-600":"bg-gray-100 text-gray-600"}`,children:o.label},o.id))})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[l==="table"&&e.jsx(fe,{programs:i,fetchClinics:W,FiltersContent:o=>e.jsx(q,{...o,customFilters:F,programs:i}),filters:C,setFilters:R,clearFilters:S,fetchCoachesForClinic:p}),l==="calendar"&&e.jsx(pe,{programs:i,fetchClinics:W,FiltersContent:o=>e.jsx(q,{...o,customFilters:F,programs:i}),filters:C,setFilters:R,clubProfile:D,clearFilters:S,fetchCoachesForClinic:p}),l==="weekly"&&e.jsx(ge,{programs:i,fetchClinics:W,FiltersContent:o=>e.jsx(q,{...o,customFilters:F,programs:i}),filters:C,setFilters:R,clearFilters:S,fetchCoachesForClinic:p})]})})]})]})}export{at as default};
