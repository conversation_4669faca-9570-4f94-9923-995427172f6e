import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as m,b as y,u as E,f as P,L as k}from"./vendor-851db8c1.js";import{o as F}from"./yup-2824f222.js";import{c as M,a as i,d as L}from"./yup-54691517.js";import{u as q}from"./react-hook-form-687afde5.js";import{M as A,A as B,G,d as O,D as U,b}from"./index-5b566256.js";import{A as R}from"./AuthLayout-69bd697c.js";/* empty css              */import{V}from"./VerificationModal-3c93a328.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const z=new A,Pe=()=>{const[n,d]=m.useState(!1);m.useState("");const C=M({email:i().email().required("Email is required"),password:i().required("Password is required"),confirmPassword:i().required("Confirm password is required").oneOf([L("password")],"Passwords must match"),name:i().required("Club name is required")});y.useContext(B);const{dispatch:c}=y.useContext(G),[v,r]=m.useState(!1),N=E();new URLSearchParams(N.search).get("redirect_uri"),P();const{register:l,handleSubmit:_,setError:x,getValues:p,formState:{errors:t}}=q({resolver:F(C)}),S=async a=>{var f,u,h,w;try{r(!0);const s=await z.callRawAPI("/v3/api/custom/courtmatchup/club/register",{email:a.email,password:a.password,role:"club",verify:!1,is_refresh:!1,slug:U(a.name),name:a.name},"POST");if(!s.error)b(c,"Succesfully Registered",4e3,"success"),d(!0),r(!1);else if(r(!1),s.validation){const g=Object.keys(s.validation);for(let o=0;o<g.length;o++){const j=g[o];x(j,{type:"manual",message:s.validation[j]})}}}catch(s){r(!1),console.log("Error",s),b(c,s==null?void 0:s.message,4e3,"error"),x("name",{type:"manual",message:(u=(f=s==null?void 0:s.response)==null?void 0:f.data)!=null&&u.message?(w=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:w.message:s==null?void 0:s.message})}};return e.jsxs(R,{children:[e.jsx("div",{className:"flex  w-full flex-grow flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("section",{className:"mt-6 flex w-full justify-center max-md:max-w-full",children:e.jsx("div",{className:"flex w-[553px] min-w-[240px] flex-col items-center pt-12",children:e.jsxs("div",{className:"flex w-[392px] max-w-full flex-col",children:[e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx("div",{className:"flex w-[74px] items-start gap-4 self-center overflow-hidden rounded-[96px] p-2",children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_139_24350)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_139_24350)"}),e.jsxs("g",{filter:"url(#filter0_d_139_24350)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M24.5 37C24.5 33.6024 25.8556 30.5213 28.0554 28.2682C30.817 30.187 32.625 33.3824 32.625 37C32.625 40.6176 30.817 43.813 28.0554 45.7318C25.8556 43.4787 24.5 40.3976 24.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M34.5 37C34.5 32.9105 32.5361 29.2796 29.5 26.9991C31.5892 25.4299 34.186 24.5 37 24.5C39.814 24.5 42.4109 25.4299 44.5 26.9991C41.4639 29.2796 39.5 32.9105 39.5 37C39.5 41.0895 41.4639 44.7204 44.5 47.0009C42.4109 48.5701 39.814 49.5 37 49.5C34.186 49.5 31.5892 48.5701 29.5 47.0009C32.5361 44.7204 34.5 41.0895 34.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M45.9446 28.2683C48.1444 30.5213 49.5 33.6024 49.5 37C49.5 40.3976 48.1444 43.4787 45.9446 45.7317C43.183 43.813 41.375 40.6176 41.375 37C41.375 33.3824 43.183 30.187 45.9446 28.2683Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_139_24350",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_139_24350"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_139_24350",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("h1",{className:"mt-4 w-full text-center text-2xl font-medium leading-none text-gray-950",children:"Set up your club"})]}),e.jsxs("form",{className:"mt-6 flex w-full flex-col gap-3",onSubmit:_(S),children:[e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx("label",{className:"mb-2 text-sm font-medium text-gray-950",htmlFor:"club-name",children:"Club name"}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5 ",id:"club-name",type:"text",placeholder:"Club name",...l("name")}),t.name&&e.jsx("p",{className:"text-sm text-red-500",children:t.name.message})]}),e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx("label",{className:"mb-2 text-sm font-medium text-gray-950",htmlFor:"email",children:"Email"}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5 ",id:"email",type:"email",placeholder:"Email",...l("email")}),t.email&&e.jsx("p",{className:"text-sm text-red-500",children:t.email.message})]}),e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx("label",{className:"mb-2 text-sm font-medium text-gray-950",htmlFor:"password",children:"Password"}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5 ",id:"password",type:"password",placeholder:"Password",...l("password")}),t.password&&e.jsx("p",{className:"text-sm text-red-500",children:t.password.message})]}),e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx("label",{className:"mb-2 text-sm font-medium text-gray-950",htmlFor:"confirmPassword",children:"Confirm Password"}),e.jsx("input",{className:"mt-1 flex w-full items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white py-2.5 pl-3 pr-2.5 ",id:"confirmPassword",type:"password",placeholder:"Confirm Password",...l("confirmPassword")}),t.confirmPassword&&e.jsx("p",{className:"text-sm text-red-500",children:t.confirmPassword.message})]}),e.jsx(O,{loading:v,type:"submit",className:"mt-6 w-full gap-2.5 self-stretch overflow-hidden whitespace-nowrap rounded-xl bg-emerald-800 px-2.5 py-3 text-center text-lg font-medium leading-none tracking-tight text-white shadow-sm",children:"Continue"})]}),e.jsx("div",{className:"mt-5 text-center",children:e.jsxs(k,{to:"/club/login",className:"text-gray-500",children:["Already a member?"," ",e.jsx("span",{className:"text-black underline underline-offset-2",children:"Login"})]})})]})})})}),n&&e.jsx(V,{isOpen:n,onClose:()=>d(!1),email:p("email"),title:"Link Sent",description:`We've send the link to: ${p("email")} Check yor email and follow the instructions there.`})]})};export{Pe as default};
