import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as c,b as I}from"./vendor-851db8c1.js";import{a as D,b as re,c as ne,d as ie,e as oe,f as de,g as X,h as ee,i as ce,j as V,k as me,l as ue,m as H,n as xe,o as pe}from"./index.esm-09a3a6b8.js";import{u as he,C as G}from"./react-hook-form-687afde5.js";import{o as fe}from"./yup-2824f222.js";import{c as ge,a as k,b as be}from"./yup-54691517.js";import{G as R,d as L,M as O,b as U,u as ye,am as je,R as ve,e as Ne,E as J,J as W,H as we}from"./index-5b566256.js";import{P as se}from"./react-phone-input-2-57d1f0dd.js";import{C as Ce,S as _e,a as ke}from"./country-state-city-282f4569.js";/* empty css              */import"./react-tooltip-7a26650a.js";const Se=({group:p,currentUserId:m,onAddMember:s,onEditName:f,onDeleteGroup:x,onViewProfile:o,onRemoveMember:b,onAddExistingUser:u,onAddFamilyMember:j,onInviteToCourtmatch:n,sentInvites:y=[]})=>{y.filter(r=>r.status==="pending"&&r.group_id===p.group_id);const[h,w]=c.useState(!1),[S,g]=c.useState(null),[M,i]=c.useState(!1),[v,C]=c.useState({}),_=c.useRef(null),B=c.useRef(null),d=c.useRef(null),P=c.useRef(null),l=localStorage.getItem("user"),t=p.group_owner_id&&parseInt(p.group_owner_id)===parseInt(m||l);return c.useEffect(()=>{const r=a=>{B.current&&!B.current.contains(a.target)&&(w(!1),i(!1)),d.current&&!d.current.contains(a.target)&&g(null),P.current&&!P.current.contains(a.target)&&i(!1)};return document.addEventListener("mousedown",r),()=>document.removeEventListener("mousedown",r)},[]),e.jsxs("div",{className:"h-fit max-h-fit rounded-xl border border-gray-100 bg-white p-6 shadow-sm transition-shadow hover:shadow-md",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600",children:e.jsx(D,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:p.group_name}),e.jsxs("p",{className:"text-sm text-gray-500",children:[p.members.length," member",p.members.length!==1?"s":""]})]})]}),t&&e.jsxs("div",{className:"relative flex items-center gap-2",children:[e.jsx("button",{onClick:()=>w(!h),className:"rounded p-1 hover:bg-gray-100",children:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",fill:"#868C98"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",fill:"#868C98"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",fill:"#868C98"}),e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),h&&e.jsx("div",{ref:B,className:"absolute right-0 z-10 mt-2 w-48 rounded-xl border-2 border-gray-200 bg-white",children:e.jsxs("div",{className:"py-1",children:[t&&e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>i(!M),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(re,{className:"mr-2"})," Add member",e.jsx("button",{className:"ml-auto flex items-center",children:e.jsx(ne,{className:"text-lg text-gray-500"})})]}),M&&e.jsx("div",{ref:P,className:"absolute left-0 top-0 z-20 ml-2 w-52 translate-x-[-105%] rounded-xl border-2 border-gray-200 bg-white sm:left-full sm:translate-x-0",children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>{u(p.id),i(!1),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(D,{className:"mr-2"})," Add existing user"]}),e.jsxs("button",{onClick:()=>{j(p.id),i(!1),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(D,{className:"mr-2"})," Add family member"]}),e.jsxs("button",{onClick:()=>{n(p.id),i(!1),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(ie,{className:"mr-2"})," Invite to CourtMatch"]})]})})]}),t&&e.jsxs("button",{onClick:()=>{f(p.id),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(oe,{className:"mr-2"})," Edit name"]}),t&&e.jsxs("button",{onClick:()=>{x(p.id),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx(de,{className:"mr-2"})," Delete group"]})]})})]})]}),e.jsx("div",{ref:_,className:"max-h-[350px] space-y-3 overflow-y-auto rounded-xl border border-gray-200 bg-gray-50/50 p-4",children:p.members.map(r=>{const a=r.guardian==l;return e.jsxs("div",{className:`group flex items-center justify-between rounded-lg p-3 transition-colors hover:bg-white ${a?"border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50":"border border-gray-100 bg-white"}`,children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full bg-gray-200 shadow-sm ring-2 ring-white",children:r.photo?e.jsx("img",{src:r.photo,alt:`${r.first_name} ${r.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200",children:e.jsx(X,{className:"h-6 w-6 text-gray-400"})})}),a&&e.jsx("div",{className:"absolute -bottom-1 -right-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 ring-2 ring-white",children:e.jsx(ee,{className:"h-3 w-3 text-white"})})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:[r.first_name," ",r.last_name]}),a&&e.jsxs("span",{className:"inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700",children:[e.jsx(ee,{className:"h-3 w-3"}),"Family"]})]}),r.family_role&&e.jsx("span",{className:"text-xs font-medium capitalize text-blue-600",children:r.family_role}),(!a||!r.family_role)&&r.email&&e.jsx("span",{className:"text-xs text-gray-500",children:r.email})]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:N=>{if(S===r.id){g(null);return}const F=N.currentTarget.getBoundingClientRect(),E=_.current.getBoundingClientRect(),Y=_.current.scrollTop,le=F.top-E.top+Y+F.height,$=E.height-(le-Y),q=100,K=$<q?"top":"bottom";if(C(z=>({...z,[r.id]:K})),K==="bottom"&&$<q){const z=q-$+10;_.current.scrollTop+=z}g(r.id)},className:"invisible rounded p-1 hover:bg-gray-100 group-hover:visible",children:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",fill:"#868C98"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",fill:"#868C98"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",fill:"#868C98"}),e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),S===r.id&&e.jsx("div",{ref:d,className:`absolute right-0 z-50 w-48 rounded-xl border-2 border-gray-200 bg-white ${v[r.id]==="top"?"bottom-full mb-2":"top-full mt-2"}`,children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>{o(r.id),g(null)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(X,{className:"mr-2"})," View profile"]}),t&&e.jsxs("button",{onClick:()=>{b(p,r),g(null)},className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx(ce,{className:"mr-2"})," Remove from group"]})]})})]})]},r.id)})})]})},ze=Se;function Z({options:p,value:m,onChange:s,placeholder:f,label:x,disabled:o=!1,className:b=""}){const[u,j]=c.useState(!1),[n,y]=c.useState(""),[h,w]=c.useState([]),S=c.useRef(null);c.useEffect(()=>{if(n.trim()==="")w(p.slice(0,10));else{const i=p.filter(v=>v.label.toLowerCase().includes(n.toLowerCase())).slice(0,10);w(i)}},[n,p]),c.useEffect(()=>{const i=p.find(v=>v.value===m);y(i?i.label:"")},[m,p]),c.useEffect(()=>{function i(v){S.current&&!S.current.contains(v.target)&&j(!1)}return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[S]);const g=i=>{y(i.target.value),j(!0),i.target.value||s({value:"",label:""})},M=i=>{y(i.label),s(i),j(!1)};return e.jsxs("div",{className:"relative",ref:S,children:[x&&e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:x}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(V,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{type:"text",value:n,onChange:g,onFocus:()=>j(!0),placeholder:f,disabled:o,className:`block w-full rounded-xl border border-gray-300 py-2 pl-10 pr-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue ${b}`})]}),u&&h.length>0&&e.jsx("div",{className:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5",children:h.map(i=>e.jsx("div",{onClick:()=>M(i),className:"cursor-pointer px-4 py-2 hover:bg-gray-100",children:i.label},i.value))})]})}let te=new O;const Be=ge().shape({firstName:k().required("First name is required"),lastName:k().required("Last name is required"),gender:k(),familyRole:k().required("Family role is required"),email:k().email("Invalid email format"),allowLogin:be(),password:k().when("allowLogin",{is:!0,then:()=>k().required("Password is required").min(6,"Password must be at least 6 characters"),otherwise:()=>k()}),phoneNumber:k().required("Phone number is required"),dateOfBirth:k().required("Date of birth is required"),address:k().required("Address is required"),country:k().required("Country is required"),city:k().required("City is required"),state:k().required("State is required"),zipCode:k().required("Zip code is required"),alternatePhone:k(),ageGroup:k().required("Age group is required"),additionalInfo:k()});function De({onSubmit:p,onClose:m,user:s,group:f,fetchData:x}){const[o,b]=c.useState(!1),[u,j]=c.useState(!1),[n,y]=c.useState(!1),{dispatch:h}=c.useContext(R),[w,S]=c.useState([]),[g,M]=c.useState([]),{register:i,handleSubmit:v,control:C,watch:_,setValue:B,formState:{errors:d}}=he({resolver:fe(Be),defaultValues:{firstName:"",lastName:"",gender:"",familyRole:"",email:"",username:"",password:"",phoneNumber:"",dateOfBirth:"",address:"",country:"",city:"",state:"",zipCode:"",alternatePhone:"",ageGroup:"",additionalInfo:"",allowLogin:!1}}),P=_("country"),l=_("state"),t=c.useMemo(()=>Ce.getAllCountries().map(a=>({value:a.isoCode,label:a.name})),[]);c.useEffect(()=>{if(P){const a=_e.getStatesOfCountry(P).map(N=>({value:N.isoCode,label:N.name}));S(a),B("state",""),B("city","")}},[P,B]),c.useEffect(()=>{if(P&&l){const a=ke.getCitiesOfState(P,l).map(N=>({value:N.name,label:N.name}));M(a)}},[P,l]),console.log("errors",d);const r=async a=>{if(f&&f.group_owner_id&&parseInt(f.group_owner_id)!==parseInt(s==null?void 0:s.id)){U(h,"Only the group owner can add family members",3e3,"error");return}y(!0),console.log(a);const N={email:a.email||"",password:a.password||"",role:"user",verify:!0,is_refresh:!1,first_name:a.firstName,last_name:a.lastName,photo:"",password_login:a.allowLogin?1:0,phone:a.phoneNumber,alternative_phone:a.alternatePhone,age_group:a.ageGroup,another:"",family_role:a.familyRole,address:a.address,city:a.city,state:a.state,country:a.country,zip_code:a.zipCode,house_no:"",date_of_birth:a.dateOfBirth,club_id:s.club_id,guardian:s.id};try{const F=await te.callRawAPI("/v3/api/custom/courtmatchup/users/register",N,"POST");console.log(F);const E=await te.callRawAPI(`/v3/api/custom/courtmatchup/user/groups/add/${f.group_id}`,{members:[F.user_id]},"POST");U(h,"Family member created successfully",3e3,"success"),await x(),m()}catch(F){U(h,F==null?void 0:F.message,3e3,"error"),console.log(F)}finally{y(!1)}};return e.jsxs("form",{onSubmit:v(r),children:[e.jsxs("div",{className:"space-y-4 py-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"First name"}),e.jsx("input",{type:"text",...i("firstName"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.firstName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.firstName.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Last name"}),e.jsx("input",{type:"text",...i("lastName"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.lastName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.lastName.message})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Gender ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsxs("select",{...i("gender"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue",children:[e.jsx("option",{value:"",children:"- select -"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"}),e.jsx("option",{value:"other",children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Family role"}),e.jsxs("select",{...i("familyRole"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue",children:[e.jsx("option",{value:"",children:"- select -"}),e.jsx("option",{value:"Parent",children:"Parent"}),e.jsx("option",{value:"Child",children:"Child"}),e.jsx("option",{value:"Sibling",children:"Sibling"}),e.jsx("option",{value:"Grandparent",children:"Grandparent"}),e.jsx("option",{value:"Spouse",children:"Spouse"}),e.jsx("option",{value:"Aunt",children:"Aunt"}),e.jsx("option",{value:"Uncle",children:"Uncle"}),e.jsx("option",{value:"Cousin",children:"Cousin"}),e.jsx("option",{value:"Guardian",children:"Guardian"}),e.jsx("option",{value:"Step-parent",children:"Step-parent"}),e.jsx("option",{value:"Step-sibling",children:"Step-sibling"}),e.jsx("option",{value:"In-law",children:"In-law"}),e.jsx("option",{value:"Godparent",children:"Godparent"})]}),d.familyRole&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.familyRole.message})]}),!u&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Email ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("input",{type:"email",...i("email"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.email.message})]}),e.jsxs("div",{className:"space-y-4 rounded-xl bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"allowLogin",...i("allowLogin"),onChange:a=>{B("allowLogin",a.target.checked),j(a.target.checked)},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("label",{htmlFor:"allowLogin",className:"text-sm font-medium text-gray-700",children:"Allow log in"})]}),u&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",...i("email"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.email.message})]}),e.jsxs("div",{className:"relative",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx("input",{type:o?"text":"password",...i("password"),className:"block w-full rounded-xl border border-gray-300 px-3 py-2 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:()=>b(!o),className:"absolute inset-y-0 right-0 flex items-center pr-3",children:o?e.jsx(me,{className:"h-5 w-5 text-gray-400"}):e.jsx(ue,{className:"h-5 w-5 text-gray-400"})})]}),d.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.password.message})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Phone number"}),e.jsx("div",{className:"mt-1 flex",children:e.jsx(G,{name:"phoneNumber",control:C,render:({field:{onChange:a,value:N}})=>e.jsx(se,{country:"us",value:N,onChange:a,containerClass:"mt-1",inputClass:"!w-full !h-[42px] !rounded-xl !border-zinc-200",buttonClass:"!border-zinc-200 !rounded-l-xl",placeholder:"(*************"})})}),d.phoneNumber&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.phoneNumber.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Date of birth"}),e.jsx("input",{type:"date",...i("dateOfBirth"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.dateOfBirth&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.dateOfBirth.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Address"}),e.jsx("input",{type:"text",...i("address"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.address&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.address.message})]}),e.jsxs("div",{children:[e.jsx(G,{name:"country",control:C,render:({field:a})=>e.jsx(Z,{label:"Country",options:t,value:a.value,onChange:N=>a.onChange(N.value),placeholder:"Search country..."})}),d.country&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.country.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(G,{name:"state",control:C,render:({field:a})=>e.jsx(Z,{label:"State",options:w,value:a.value,onChange:N=>a.onChange(N.value),placeholder:"Search state...",disabled:!P})}),d.state&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.state.message})]}),e.jsxs("div",{children:[e.jsx(G,{name:"city",control:C,render:({field:a})=>e.jsx(Z,{label:"City",options:g,value:a.value,onChange:N=>a.onChange(N.value),placeholder:"Search city...",disabled:!l})}),d.city&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.city.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Zip code"}),e.jsx("input",{type:"text",...i("zipCode"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),d.zipCode&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.zipCode.message})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Alternate phone number"," ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("div",{className:"mt-1 flex",children:e.jsx(G,{name:"alternatePhone",control:C,render:({field:{onChange:a,value:N}})=>e.jsx(se,{country:"us",value:N,onChange:a,containerClass:"mt-1",inputClass:"!w-full !h-[42px] !rounded-xl !border-zinc-200",buttonClass:"!border-zinc-200 !rounded-l-xl",placeholder:"(*************"})})}),d.alternatePhone&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.alternatePhone.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Age group"}),e.jsxs("select",{...i("ageGroup"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue",children:[e.jsx("option",{value:"",children:"- select -"}),e.jsx("option",{value:"toddler",children:"3-5 years (Toddler)"}),e.jsx("option",{value:"child",children:"6-12 years (Child)"}),e.jsx("option",{value:"teen",children:"13-17 years (Teen)"}),e.jsx("option",{value:"young_adult",children:"18-25 years (Young Adult)"}),e.jsx("option",{value:"adult",children:"26-39 years (Adult)"}),e.jsx("option",{value:"middle_aged",children:"40-59 years (Middle-aged)"}),e.jsx("option",{value:"senior",children:"60+ years (Senior)"})]}),d.ageGroup&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:d.ageGroup.message})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Additional info ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("textarea",{...i("additionalInfo"),rows:3,className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"})]})]}),e.jsxs("div",{className:"sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:m,children:"Cancel"}),e.jsx(L,{type:"submit",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",loading:n,children:n?"Saving...":"Add family member"})]})]})}let Ue=new O;function Ze({users:p,onClose:m,fetchData:s}){const{dispatch:f}=I.useContext(R),{user_subscription:x,user_permissions:o}=ye(),[b,u]=c.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),[j,n]=c.useState({groupName:"",isFamilyGroup:!1,selectedUsers:[]}),[y,h]=c.useState({}),[w,S]=c.useState(""),[g,M]=c.useState(!1),i=t=>{const{name:r,value:a,type:N,checked:F}=t.target;n(E=>({...E,[r]:N==="checkbox"?F:a})),y[r]&&h(E=>({...E,[r]:""}))},v=t=>{n(r=>({...r,selectedUsers:r.selectedUsers.includes(t)?r.selectedUsers.filter(a=>a!==t):[...r.selectedUsers,t]})),y.selectedUsers&&h(r=>({...r,selectedUsers:""}))},C=t=>{n(r=>({...r,selectedUsers:r.selectedUsers.filter(a=>a!==t)}))},_=t=>`${t.first_name} ${t.last_name}`,B=p.filter(t=>_(t).toLowerCase().includes(w.toLowerCase())),d=p.filter(t=>j.selectedUsers.includes(t.id)),P=()=>{const t={};return j.groupName.trim()||(t.groupName="Group name is required"),j.selectedUsers.length===0&&(t.selectedUsers="Please select at least one member"),h(t),Object.keys(t).length===0},l=async()=>{if(!(x!=null&&x.planId)){u({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to create groups",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(o!=null&&o.allowGroups)){u({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${o==null?void 0:o.planName}) does not include group creation. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(P()){M(!0);try{const t={name:j.groupName.trim(),members:j.selectedUsers,type:j.isFamilyGroup?1:0};await Ue.callRawAPI("/v3/api/custom/courtmatchup/user/groups/create",t,"POST")&&(m(),U(f,"Group created successfully",3e3,"success"),s())}catch(t){console.error("Error creating group:",t),u({isOpen:!0,title:"Group Creation Error",message:(t==null?void 0:t.message)||"Error creating group",type:"error"})}finally{M(!1)}}};return console.log({selectedUsersList:d}),e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx(je,{isOpen:b.isOpen,onClose:()=>u({...b,isOpen:!1}),title:b.title,message:b.message,actionButtonText:b.actionButtonText,actionButtonLink:b.actionButtonLink,type:b.type}),e.jsxs("div",{className:"flex-1 overflow-y-auto",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block font-medium text-gray-700",children:"Group name"}),e.jsx("input",{type:"text",name:"groupName",value:j.groupName,onChange:i,className:`mt-1 block w-full rounded-md border ${y.groupName?"border-red-500":"border-gray-300"} px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue`}),y.groupName&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:y.groupName})]}),e.jsx("div",{className:"mb-6 mt-6 text-gray-600",children:"An invite will be sent out to the users you select via email. Once they accept, they would successfully be added to the group."}),e.jsxs("div",{className:"space-y-4",children:[d.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 rounded-lg bg-gray-50 p-2",children:d.map(t=>e.jsxs("div",{className:"flex items-center space-x-1 rounded-full bg-white px-2 py-1 shadow-sm",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:_(t),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:_(t)}),e.jsx("button",{type:"button",onClick:()=>C(t.id),className:"ml-1 rounded-full p-0.5 hover:bg-gray-100",children:e.jsx(H,{className:"h-4 w-4 text-gray-500"})})]},t.id))}),e.jsxs("div",{className:"relative",children:[e.jsx(V,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"search by name",value:w,onChange:t=>S(t.target.value),className:"block w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"})]}),e.jsx("div",{className:`max-h-96 overflow-y-auto rounded-xl bg-gray-50 p-2 ${y.selectedUsers?"border-2 border-red-500":""}`,children:B.map(t=>e.jsxs("div",{className:"flex items-center space-x-3 py-2",children:[e.jsx("input",{type:"checkbox",checked:j.selectedUsers.includes(t.id),onChange:()=>v(t.id),className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:_(t),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:_(t)})]},t.id))}),y.selectedUsers&&e.jsx("p",{className:"text-sm text-red-500",children:y.selectedUsers})]})]}),e.jsxs("div",{className:"sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:m,children:"Cancel"}),e.jsx(L,{type:"button",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:l,loading:g,children:g?"Saving...":"Create group"})]})]})}const A=I.memo(({label:p,field:m,value:s,onChange:f,isEditMode:x,editLoading:o,user:b})=>e.jsx("div",{className:"mb-4",children:e.jsxs("div",{children:[e.jsx("label",{htmlFor:m,className:"text-sm uppercase text-gray-500",children:p}),x?e.jsx("input",{type:"text",id:m,name:m,value:s,onChange:f,className:"mt-1 w-full rounded-xl border border-gray-300 p-2 focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",disabled:o,autoComplete:"off"}):e.jsx("div",{className:"text-gray-900",children:b[m]||"Not set"})]})}));A.displayName="ProfileField";const Pe=({isOpen:p,onClose:m,user:s,fetchData:f})=>{var M,i;if(!s)return null;const{dispatch:x}=I.useContext(R),[o,b]=I.useState(!1),[u,j]=I.useState(!1),[n,y]=I.useState({}),h=I.useRef(null);I.useEffect(()=>{s&&y({first_name:s.first_name||"",last_name:s.last_name||"",gender:s.gender||"",family_role:s.family_role||"",email:s.email||"",phone:s.phone||"",date_of_birth:s.date_of_birth||"",address:s.address||"",city:s.city||"",state:s.state||"",zip_code:s.zip_code||"",alternative_phone:s.alternative_phone||"",age_group:s.age_group||"",bio:s.bio||""})},[s]);const w=async v=>{v==null||v.preventDefault();try{b(!0);const C=new O,_={first_name:n.first_name,last_name:n.last_name,family_role:n.family_role,email:n.email,phone:n.phone,age_group:n.age_group,id:s.id};C.setTable("user"),(await C.callRestAPI(_,"PUT")).error||(U(x,"All changes saved successfully",3e3,"success"),j(!1),typeof m=="function"&&(m(),f()))}catch(C){U(x,C==null?void 0:C.message,3e3,"error")}finally{b(!1)}},S=v=>{v==null||v.preventDefault(),j(!1),y({first_name:s.first_name||"",last_name:s.last_name||"",gender:s.gender||"",family_role:s.family_role||"",email:s.email||"",phone:s.phone||"",date_of_birth:s.date_of_birth||"",address:s.address||"",city:s.city||"",state:s.state||"",zip_code:s.zip_code||"",alternative_phone:s.alternative_phone||"",age_group:s.age_group||"",bio:s.bio||""})},g=I.useCallback(v=>{const{name:C,value:_}=v.target;y(B=>({...B,[C]:_}))},[]);return e.jsxs(ve,{isOpen:p,onClose:m,title:"Profile details",showFooter:!1,children:[o&&e.jsx(Ne,{}),e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{className:"text-sm uppercase text-gray-500",children:"MEMBER"}),u?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{type:"button",onClick:S,className:"flex items-center gap-1 rounded-xl border border-gray-300 px-4 py-2 text-sm text-gray-600 hover:bg-gray-50",disabled:o,children:[e.jsx(H,{className:"h-4 w-4"}),"Cancel"]}),e.jsxs("button",{type:"submit",form:"profile-form",className:"flex items-center gap-1 rounded-xl bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700 disabled:opacity-50",disabled:o,children:[e.jsx(xe,{className:"h-4 w-4"}),o?"Saving...":"Save All"]})]}):e.jsxs("button",{type:"button",onClick:()=>j(!0),className:"flex items-center gap-1 rounded-xl bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700",children:[e.jsx(pe,{className:"h-4 w-4"}),"Edit Profile"]})]}),e.jsxs("div",{className:"mb-6 flex items-center space-x-3",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full bg-gray-200",children:s.photo?e.jsx("img",{src:s.photo,alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"}):e.jsxs("div",{className:"flex h-full w-full items-center justify-center bg-primaryBlue text-lg text-white",children:[(M=s.first_name)==null?void 0:M.charAt(0),(i=s.last_name)==null?void 0:i.charAt(0)]})}),e.jsxs("h2",{className:"text-xl font-medium",children:[s.first_name," ",s.last_name]})]}),e.jsx("div",{className:"mb-4 text-sm uppercase text-gray-500",children:"DETAILS"}),e.jsxs("form",{id:"profile-form",ref:h,onSubmit:w,className:"space-y-4",children:[e.jsx(A,{label:"First Name",field:"first_name",value:n.first_name,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Last Name",field:"last_name",value:n.last_name,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Gender",field:"gender",value:n.gender,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Family Role",field:"family_role",value:n.family_role,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Email",field:"email",value:n.email,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Phone Number",field:"phone",value:n.phone,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Date of Birth",field:"date_of_birth",value:n.date_of_birth,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Address",field:"address",value:n.address,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"City",field:"city",value:n.city,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"State",field:"state",value:n.state,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Zip Code",field:"zip_code",value:n.zip_code,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Alternate Phone Number",field:"alternative_phone",value:n.alternative_phone,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsx(A,{label:"Age Group",field:"age_group",value:n.age_group,onChange:g,isEditMode:u,editLoading:o,user:s}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"bio",className:"mb-2 text-sm uppercase text-gray-500",children:"Additional Info"}),e.jsx("div",{children:u?e.jsx("textarea",{id:"bio",name:"bio",value:n.bio||"",onChange:g,className:"mt-1 w-full rounded-xl border border-gray-300 p-2 focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",disabled:o,rows:4}):e.jsx("div",{className:"text-sm text-gray-900",children:s.bio||"Not set"})})]})]})]})]})},Qe=Pe;let Q=new O;function Ve({title:p,onClose:m,message:s,group:f,user:x}){const[o,b]=c.useState(""),{dispatch:u}=c.useContext(R),[j,n]=c.useState(!1),y=async()=>{try{n(!0);const h=await Q.callRawAPI(`/v3/api/custom/courtmatchup/user/groups/invite/${f.id}`,{email:o},"POST");Q.setTable("activity_logs"),await Q.callRestAPI({user_id:x==null?void 0:x.id,action:"Invited user to group",activity_type:J.group,action_type:W.CREATE,data:JSON.stringify({group:f}),club_id:x==null?void 0:x.club_id,description:"Invited user to group"},"POST"),h.error?U(u,"Failed to send invitation",3e3,"error"):(U(u,"Invitation sent successfully"),m())}catch(h){U(u,h.message,3e3,"error")}finally{n(!1)}};return e.jsx("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"contact-info-popover z-50 w-[400px] rounded-2xl bg-white shadow-lg",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"mb-5 text-xl font-medium text-gray-900",children:p}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("p",{className:"mb-2 text-base text-gray-500",children:"Type the mail of the person you want to invite:"}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx("label",{className:"mb-2 block font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"text",value:o,onChange:h=>b(h.target.value),className:"h-full w-full rounded-xl border  border-gray-300 bg-gray-50 bg-transparent px-2 py-3 outline-none"})]})]}),e.jsx("p",{children:s})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 border-t border-gray-200 p-5",children:[e.jsx("button",{className:"rounded-xl border border-gray-300 px-5 py-2 text-gray-500 ",onClick:m,children:"Cancel"}),e.jsx(L,{className:"rounded-xl bg-[#176448] px-5 py-2 text-white hover:bg-[#176448]",loading:j,onClick:y,children:"Send Invitation"})]})]})})}let ae=new O;function He({onClose:p,group:m,fetchData:s,users:f,user:x,sentInvites:o=[]}){const{dispatch:b}=I.useContext(R),[u,j]=c.useState("");console.log("group",m);const[n,y]=c.useState({selectedUsers:[],selectedUsersIds:[]}),[h,w]=c.useState({}),[S,g]=c.useState(!1),M=l=>{y(t=>({...t,selectedUsers:t.selectedUsers.includes(l)?t.selectedUsers.filter(r=>r!==l):[...t.selectedUsers,l],selectedUsersIds:t.selectedUsersIds.includes(l.id)?t.selectedUsersIds.filter(r=>r!==l.id):[...t.selectedUsersIds,l.id]})),h.selectedUsers&&w(t=>({...t,selectedUsers:""}))},i=l=>{y(t=>({...t,selectedUsers:t.selectedUsers.filter(r=>r!==l)}))},v=l=>`${l.first_name} ${l.last_name}`,_=f.filter(l=>{const t=m.members.some(a=>a.id===l.id),r=o.some(a=>a.invitee_email===l.email&&a.group_id===m.group_id);return!t&&!r}).filter(l=>v(l).toLowerCase().includes(u.toLowerCase())),B=o.filter(l=>l.status==="pending"&&l.group_id===m.group_id),d=f.filter(l=>n.selectedUsers.includes(l.id));console.log("formData",n);const P=async()=>{if(m&&m.group_owner_id&&parseInt(m.group_owner_id)!==parseInt(x==null?void 0:x.id)){U(b,"Only the group owner can add members",3e3,"error");return}if(n.selectedUsers.length===0){U(b,"Please select at least one user",5e3,"warning");return}g(!0);try{const l=n.selectedUsers.map(async r=>{const a=f.find(N=>N.id===r.id);if(a){const N=`${window.location.origin}/user/group/accept-invite/${m.group_id}`;await ae.callRawAPI(`/v3/api/custom/courtmatchup/user/groups/invite/${m.group_id}`,{email:a.email,link:N},"POST")}});await Promise.all(l);const t={id:m.group_id,members:n.selectedUsers};console.log("payload",t),await we(ae,{user_id:x==null?void 0:x.id,activity_type:J.group,action_type:W.CREATE,data:t,club_id:x==null?void 0:x.club_id,description:`Added ${n.selectedUsers.length} members to group`}),U(b,"Members invited successfully",5e3,"success"),await s(),p()}catch(l){console.error("Error adding users:",l),U(b,l.message,5e3,"error")}finally{g(!1)}};return e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"space-y-4",children:[B.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium text-gray-700",children:"Pending Invites"}),e.jsx("div",{className:"flex flex-wrap gap-2 rounded-lg bg-gray-50 p-2",children:B.map(l=>{var t,r;return e.jsxs("div",{className:"flex items-center space-x-1 rounded-full bg-white px-2 py-1 shadow-sm",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:e.jsx("span",{className:"text-xs text-gray-500",children:((r=(t=l.invitee_email)==null?void 0:t.charAt(0))==null?void 0:r.toUpperCase())||"?"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:l.invitee_email}),e.jsx("span",{className:"ml-1 rounded-full bg-yellow-100 px-2 py-0.5 text-xs text-yellow-800",children:"Pending"})]},l.invite_id)})})]}),d.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 rounded-lg bg-gray-50 p-2",children:d.map(l=>e.jsxs("div",{className:"flex items-center space-x-1 rounded-full bg-white px-2 py-1 shadow-sm",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full",children:e.jsx("img",{src:(l==null?void 0:l.photo)||"/default-avatar.png",alt:v(l),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:v(l)}),e.jsx("button",{type:"button",onClick:()=>i(l.id),className:"ml-1 rounded-full p-0.5 hover:bg-gray-100",children:e.jsx(H,{className:"h-4 w-4 text-gray-500"})})]},l.id))}),e.jsxs("div",{className:"relative",children:[e.jsx(V,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"search by name",value:u,onChange:l=>j(l.target.value),className:"block w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"})]}),e.jsx("div",{className:`max-h-96 overflow-y-auto rounded-xl bg-gray-50 p-2 ${h.selectedUsers?"border-2 border-red-500":""}`,children:_.map(l=>e.jsxs("div",{className:"flex items-center space-x-3 py-2",children:[e.jsx("input",{type:"checkbox",checked:n.selectedUsersIds.includes(l.id),onChange:()=>M(l),className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full",children:e.jsx("img",{src:(l==null?void 0:l.photo)||"/default-avatar.png",alt:v(l),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:v(l)})]},l.id))}),h.selectedUsers&&e.jsx("p",{className:"text-sm text-red-500",children:h.selectedUsers})]})}),e.jsxs("div",{className:"sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:p,children:"Cancel"}),e.jsx(L,{type:"button",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:P,loading:S,children:S?"Saving...":"Add members"})]})]})}let T=new O;function Je({title:p,onClose:m,group:s,user:f}){const[x,o]=c.useState((s==null?void 0:s.name)||""),{dispatch:b}=c.useContext(R),[u,j]=c.useState(!1);console.log(s);const n=(s==null?void 0:s.group_owner_id)&&parseInt(s.group_owner_id)===parseInt(f==null?void 0:f.id),y=async()=>{if(!n){U(b,"Only the group owner can edit the group name",3e3,"error");return}try{j(!0),T.setTable("user_groups");const h={id:s.group_id,name:x},w=await T.callRestAPI(h,"PUT");T.setTable("activity_logs"),await T.callRestAPI({user_id:f==null?void 0:f.id,action:"Updated group name",activity_type:J.group,action_type:W.UPDATE,data:JSON.stringify({group:s}),club_id:f==null?void 0:f.club_id,description:"Updated group name"},"POST"),w.message&&(U(b,"Group name updated successfully"),m(!0))}catch(h){U(b,h.message,3e3,"error")}finally{j(!1)}};return e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"contact-info-popover z-50 w-[400px] rounded-2xl bg-white shadow-lg",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"mb-5 text-xl font-medium text-gray-900",children:p}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("p",{className:"mb-2 text-base text-gray-500",children:"Enter new name for the group:"}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx("label",{className:"mb-2 block font-medium text-gray-700",children:"Group Name"}),e.jsx("input",{type:"text",value:x,onChange:h=>o(h.target.value),disabled:!n,className:`h-full w-full rounded-xl border border-gray-300 bg-gray-50 bg-transparent px-2 py-3 outline-none ${n?"":"cursor-not-allowed opacity-50"}`}),!n&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Only the group owner can edit the group name."})]})]})})]}),e.jsxs("div",{className:"flex justify-end gap-2 border-t border-gray-200 p-5",children:[e.jsx("button",{className:"rounded-xl border border-gray-300 px-5 py-2 text-gray-500",onClick:()=>m(!1),children:"Cancel"}),e.jsx(L,{className:`rounded-xl px-5 py-2 text-white ${n?"bg-[#176448] hover:bg-[#176448]":"cursor-not-allowed bg-gray-400"}`,loading:u,onClick:y,disabled:!n,children:"Update Name"})]})]})})}export{De as A,Ze as C,Je as E,ze as G,Ve as I,Qe as U,He as a};
