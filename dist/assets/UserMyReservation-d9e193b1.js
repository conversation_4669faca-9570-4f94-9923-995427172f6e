import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{f as H,r as n,b as i}from"./vendor-851db8c1.js";import{_ as V,aG as J,Z as c,a3 as P,v as k,M as ds,G as is,e as Ns,b as Y,d as Bs,A as os,R as Os,ab as Gs,T as Ys,i as Us,X as Hs,Y as js,t as Vs}from"./index-5b566256.js";import{c as zs,a as Z}from"./yup-54691517.js";import{u as qs}from"./react-hook-form-687afde5.js";import{o as Zs}from"./yup-2824f222.js";import{P as Ks}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Js from"./Skeleton-1e8bf077.js";import{L as Qs}from"./LoadingOverlay-87926629.js";import{R as Q,T as X,P as W,F as v,C as ss,G as bs,L as Cs}from"./ReservationStatus-5ced670f.js";import{h as U}from"./moment-a9aaa855.js";import{S as Xs}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";function Ws({reservation:e,club:E,clubSports:d,players:t,onCancelClick:m}){var b;const h=H(),f=V(e==null?void 0:e.reservation_updated_at),N=J(e,d);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===c.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Q,{}),s.jsx(X,{timeLeft:f})]})||(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsx(W,{})||(e==null?void 0:e.booking_status)===c.FAIL&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===c.CANCELLED&&s.jsx(ss,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(b=d==null?void 0:d.find(a=>a.id===(e==null?void 0:e.sport_id)))==null?void 0:b.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:U(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[P(e==null?void 0:e.start_time)," -"," ",P(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(a=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:a.photo||"/default-avatar.png",alt:a.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:a.first_name||a.last_name?`${a.first_name} ${a.last_name}`:"Player"})]},a.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:k(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:k(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:k((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==c.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{h(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:f==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${f==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:f==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==c.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{h(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),N&&(e==null?void 0:e.booking_status)===c.SUCCESS&&(e==null?void 0:e.booking_status)!==c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:m,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}function vs({reservation:e,club:E,clubSports:d,players:t,coach:m,onCancelClick:h}){var a;const f=H(),N=V(e==null?void 0:e.reservation_updated_at),b=J(e,d);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{children:(e==null?void 0:e.booking_status)===c.PENDING&&s.jsx(Q,{})||(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsx(W,{})||(e==null?void 0:e.booking_status)===c.FAIL&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===c.CANCELLED&&s.jsx(ss,{})}),s.jsx("div",{children:s.jsx(X,{timeLeft:N})})]})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(a=d==null?void 0:d.find(g=>g.id===(e==null?void 0:e.sport_id)))==null?void 0:a.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:U(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[P(e==null?void 0:e.start_time)," -"," ",P(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"COACH"})}),s.jsx("div",{className:"flex flex-col gap-2",children:m&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:m.photo||"/default-avatar.png",alt:m.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:m.first_name||m.last_name?`${m.first_name} ${m.last_name}`:"Coach"})]},m.user_id)})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(g=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:g.photo||"/default-avatar.png",alt:g.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:g.first_name||g.last_name?`${g.first_name} ${g.last_name}`:"Player"})]},g.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:k(E==null?void 0:E.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:"$12.50"})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:k((e==null?void 0:e.club_fee)+12.5)})]})]}),(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{f(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),b&&(e==null?void 0:e.booking_status)===c.SUCCESS&&(e==null?void 0:e.booking_status)!==c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:h,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}function se({reservation:e,club:E,clubSports:d,players:t,onCancelClick:m}){var b;const h=H(),f=V(e==null?void 0:e.reservation_updated_at),N=J(e,d);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===c.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Q,{}),s.jsx(X,{timeLeft:f})]})||(e==null?void 0:e.booking_status)===c.SUCCESS&&s.jsx(W,{})||(e==null?void 0:e.booking_status)===c.FAIL&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===c.CANCELLED&&s.jsx(ss,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(b=d==null?void 0:d.find(a=>a.id===(e==null?void 0:e.sport_id)))==null?void 0:b.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:U(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[P(e==null?void 0:e.start_time)," -"," ",P(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t==null?void 0:t.map(a=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:a.photo||"/default-avatar.png",alt:a.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:a.first_name||a.last_name?`${a.first_name} ${a.last_name}`:"Player"})]},a.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:k(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:k(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:k((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{h(`/user/payment-receipt/${e==null?void 0:e.reservation_id}?type=clinic`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),N&&(e==null?void 0:e.booking_status)==c.SUCCESS&&(e==null?void 0:e.booking_status)!=c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:m,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}const ee=new ds;function le({reservation:e,club:E,clubSports:d,players:t,onCancelClick:m}){var R;const h=H(),f=V(e==null?void 0:e.reservation_updated_at),{dispatch:N}=n.useContext(is),[b,a]=n.useState(!1),g=J(e,d),I=async u=>{if(!u){Y(N,"Email is required",5e3,"error");return}try{a(!0);const M=await ee.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${e==null?void 0:e.buddy_id}/send-mail?email=${u}`,{},"GET");console.log(M),Y(N,"Email reminder sent",5e3,"success")}catch(M){console.log(M),Y(N,M==null?void 0:M.message,5e3,"error")}finally{a(!1)}};return s.jsxs("div",{children:[b&&s.jsx(Ns,{}),s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsxs("div",{children:[(e==null?void 0:e.num_needed)==(e==null?void 0:e.num_players)&&s.jsx(bs,{title:"Group full"}),(e==null?void 0:e.num_needed)!=(e==null?void 0:e.num_players)&&s.jsx(Cs,{numberOfBuddies:e==null?void 0:e.num_needed})]})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"})}),U(e==null?void 0:e.reservation_created_at).format("MMM D, YYYY")]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DTE & TIME"})}),U(e==null?void 0:e.booking_date).format("MMM D, YYYY")," •"," ",P(e==null?void 0:e.start_time)," -"," ",P(e==null?void 0:e.end_time)]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(R=d==null?void 0:d.find(u=>u.id===(e==null?void 0:e.sport_id)))==null?void 0:R.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"NOTES"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[e==null?void 0:e.ntrp," ",(e==null?void 0:e.max_ntrp)&&`- ${e==null?void 0:e.max_ntrp}`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"})}),s.jsx("p",{className:"mt-1 font-medium",children:`${e==null?void 0:e.num_needed}/${e==null?void 0:e.num_players}`})]}),s.jsxs("div",{className:"py-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"}),s.jsx("button",{className:"rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700",children:"Email all"})]}),s.jsx("div",{className:"mt-4 flex flex-col gap-4",children:t.map(u=>s.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 p-2",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:u.photo||"/default-avatar.png",alt:u.first_name,className:"h-10 w-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium capitalize",children:u.first_name||u.last_name?`${u.first_name} ${u.last_name}`:"Player"}),s.jsx("p",{className:"text-sm text-gray-500",children:u.email})]})]}),s.jsxs("div",{className:"flex flex-col items-end gap-3",children:[s.jsx("button",{onClick:()=>I(u.email),className:"rounded-lg bg-white px-2 py-1 text-sm text-gray-700",children:"Send email"}),s.jsxs("p",{className:"text-sm text-gray-700",children:["NTRP: ",u.ntrp]})]})]},u.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:k(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:k(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:k((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==c.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{h(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:f==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${f==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:f==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==c.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{h(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),g&&(e==null?void 0:e.booking_status)===c.SUCCESS&&(e==null?void 0:e.booking_status)!==c.CANCELLED&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:m,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})]})}const te=({isOpen:e,onClose:E,onCancel:d,loading:t=!1})=>s.jsxs("div",{className:`fixed inset-0 z-[999999] flex items-center justify-center ${e?"":"hidden"}`,children:[s.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),s.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[s.jsxs("div",{className:"p-6",children:[s.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Cancel reservation"}),s.jsx("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),s.jsx("p",{className:"text-sm text-white",children:"Are you sure you want to cancel this reservation?"})]})})]}),s.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[s.jsx("button",{onClick:E,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),s.jsx(Bs,{onClick:d,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:t,children:"Cancel reservation"})]})]})]});let K=new ds;function ce({isOpen:e,onClose:E,clubSports:d,reservation:t,club:m,onReservationCanceled:h}){const{dispatch:f}=n.useContext(is),{dispatch:N}=n.useContext(os),[b,a]=n.useState(!1),[g,I]=n.useState([]),[R,u]=n.useState(null),[M,T]=n.useState(!1),[es,A]=n.useState(!1);async function F(){try{const C=await Gs(f,N,"user",JSON.parse(t==null?void 0:t.player_ids),"user|user_id");I(C.list)}catch(C){console.error(C)}}async function ls(){try{K.setTable("user");const C=await K.callRestAPI({id:t==null?void 0:t.coach_id},"GET");u(C.model)}catch(C){console.error(C)}}const ms=async()=>{A(!0);try{K.setTable("booking"),await K.callRestAPI({id:t==null?void 0:t.booking_id,status:c.CANCELLED},"PUT"),Y(f,"Reservation cancelled successfully",3e3,"success"),T(!1),t&&(t.booking_status=c.CANCELLED),h&&h()}catch(C){console.error("Error cancelling reservation:",C),Y(f,C.message||"Error cancelling reservation",3e3,"error")}finally{A(!1)}};return n.useEffect(()=>{(async()=>(a(!0),await F(),await ls(),a(!1)))()},[t]),t?b?s.jsx(Ns,{}):(console.log({reservation:t,sports:d,players:g,coach:R}),s.jsxs(s.Fragment,{children:[s.jsxs(Os,{isOpen:e,onClose:E,title:t.booking_type==="Court"?"Court details":t.booking_type==="Coach"?"Coach details":t.booking_type==="Clinic"?"Clinic details":"Details",showFooter:!1,className:"!p-0",children:[t.booking_type==="Court"&&s.jsx(Ws,{reservation:t,club:m,clubSports:d,players:g,onCancelClick:()=>T(!0)}),t.booking_type==="Find Buddy"&&s.jsx(le,{reservation:t,club:m,clubSports:d,players:g,onCancelClick:()=>T(!0)}),t.booking_type==="Coach"&&s.jsx(vs,{reservation:t,club:m,clubSports:d,players:g,coach:R,onCancelClick:()=>T(!0)}),t.booking_type==="Clinic"&&s.jsx(se,{reservation:t,club:m,clubSports:d,players:g,onCancelClick:()=>T(!0)})]}),s.jsx(te,{isOpen:M,onClose:()=>T(!1),onCancel:ms,loading:es})]})):null}let D=new ds,as=new Ys;const ys=[{header:"Date & Time",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Booking Type",accessor:"booking_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Players",accessor:"players",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"reservation_status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{1:"Active",0:"Inactive"}}],Ke=()=>{const{dispatch:e,state:E}=i.useContext(is),{dispatch:d}=i.useContext(os),[t,m]=i.useState([]),[h,f]=i.useState(10),[N,b]=i.useState(0);i.useState(0);const[a,g]=i.useState(0),[I,R]=i.useState(!1),[u,M]=i.useState(!1),[T,es]=i.useState(!1);i.useState(!1),i.useState([]),i.useState([]),i.useState("eq");const[A,F]=i.useState(!0),[ls,ms]=i.useState(!1),[C,ae]=i.useState(!1);i.useState(),H();const xs=i.useRef(null),[de,_s]=i.useState(!1),[ns,ts]=i.useState(null);i.useState([]),n.useState(!1);const[Ss,ks]=n.useState([]);n.useState([]),n.useState([]);const[L,Es]=n.useState("upcoming");n.useState(!1),n.useState(!1);const[Ls,ws]=n.useState(null),[B,Ms]=n.useState([]),[_,us]=n.useState(null),[ps,rs]=n.useState(null),Ps=[{id:"upcoming",label:"Upcoming",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.05153 8.29769L3.13684 14.4527C3.21675 14.906 3.64897 15.2086 4.10222 15.1287L8.42993 14.3656M2.05153 8.29769L1.61741 5.83567C1.53749 5.38242 1.84013 4.95021 2.29338 4.87029L11.7311 3.20616C12.1844 3.12624 12.6166 3.42888 12.6965 3.88213L13.1306 6.34414L2.05153 8.29769ZM13.3333 9.79243V11.6674L15 13.3341M18.5417 11.6674C18.5417 14.5439 16.2098 16.8758 13.3333 16.8758C10.4569 16.8758 8.125 14.5439 8.125 11.6674C8.125 8.79095 10.4569 6.4591 13.3333 6.4591C16.2098 6.4591 18.5417 8.79095 18.5417 11.6674Z",stroke:"black","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})},{id:"past",label:"Past",icon:()=>s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("rect",{width:"20",height:"20",fill:"white"}),s.jsx("path",{d:"M12.5 7.91602L8.75001 12.4993L7.08334 10.8327M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.74281 17.7077 2.29167 14.2565 2.29167 9.99935C2.29167 5.74215 5.74281 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{id:"cancelled",label:"Cancelled",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12.5 7.49935L7.5 12.4993M12.5 12.4993L7.5 7.49935M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.7428 17.7077 2.29166 14.2565 2.29166 9.99935C2.29166 5.74215 5.7428 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})}],Ts=zs({id:Z(),email:Z(),role:Z(),status:Z()});qs({resolver:Zs(Ts)});function Ds(){r()}function Rs(){r()}async function r(l,S,O={},o=[]){F(!(C||ls)),console.log("Selected family member:",_);try{let x;if(_&&_.value!=="all"&&_.value!=="me"?L==="past"||L==="cancelled"?x=await D.callRawAPI(`/v3/api/custom/courtmatchup/user/past-family-reservations/${_.value.id}`,{},"GET"):x=await D.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${_.value.id}`,{},"GET"):L==="past"||L==="cancelled"?x=await D.callRawAPI("/v3/api/custom/courtmatchup/user/past-reservations",{},"GET"):x=await D.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET"),x){F(!1);const $=new Date;$.setHours(0,0,0,0);let j=x.list||x.reservations||[];if(console.log("Initial user reservations:",j.length),_&&_.value==="all"&&B.length>0)try{const p=localStorage.getItem("user"),w=B.filter(y=>y.id.toString()!==p);console.log(`Fetching reservations for ${w.length} family members`);const z=w.map(y=>L==="past"||L==="cancelled"?D.callRawAPI(`/v3/api/custom/courtmatchup/user/past-family-reservations/${y.id}`,{},"GET"):D.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${y.id}`,{},"GET"));(await Promise.all(z)).forEach((y,G)=>{if(y&&(y.list||y.reservations)){const q=y.list||y.reservations||[];console.log(`Family member ${G+1} reservations:`,q.length),j=[...j,...q]}}),console.log("Total reservations before deduplication:",j.length),j=j.filter((y,G,q)=>G===q.findIndex(Fs=>Fs.reservation_id===y.reservation_id)),console.log("Combined reservations after deduplication:",j.length)}catch(p){console.error("Error fetching family reservations:",p)}L==="upcoming"?j=j.filter(p=>{const w=new Date(p.booking_date);return w.setHours(0,0,0,0),w>=$&&p.booking_status!==c.CANCELLED&&(p.booking_status===c.PENDING||p.booking_status===c.SUCCESS)}):L==="past"?j=j.filter(p=>{const w=new Date(p.booking_date);return w.setHours(0,0,0,0),w<$&&p.booking_status!==c.CANCELLED&&(p.booking_status===c.PENDING||p.booking_status===c.SUCCESS||p.booking_status===c.FAIL)}):L==="cancelled"&&(j=j.filter(p=>p.booking_status===c.CANCELLED)),m(j)}}catch(x){F(!1),console.log("ERROR",x),Vs(d,x.message)}}const As=async()=>{try{const l=localStorage.getItem("user"),S=await as.getOne("user",l,{}),O=await D.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${S.model.club_id}`,{},"GET");ks(O.sports),ws(O.model)}catch(l){console.error(l)}},$s=async()=>{try{const l=localStorage.getItem("user"),S=await as.getList("user",{filter:[`guardian,eq,${l}`,"role,cs,user"]});Ms(S.list)}catch(l){console.error("Error fetching family members:",l)}},Is=async()=>{try{const l=localStorage.getItem("user"),S=await as.getOne("user",l,{});rs(S.model)}catch(l){console.error("Error fetching user profile:",l)}};i.useEffect(()=>{e({type:"SETPATH",payload:{path:"my-reservations"}});const S=setTimeout(async()=>{await r(1,h,{}),await As(),await $s(),await Is()},700);return()=>{clearTimeout(S)}},[]),i.useEffect(()=>{r(1,h,{})},[L]),i.useEffect(()=>{_!==null&&r(1,h,{})},[_]),i.useEffect(()=>{_===null&&us({value:"all",label:"All Reservations"})},[B,ps]);const hs=l=>{xs.current&&!xs.current.contains(l.target)&&es(!1)};i.useEffect(()=>(document.addEventListener("mousedown",hs),()=>{document.removeEventListener("mousedown",hs)}),[]);const fs=l=>{ts(l),_s(!0)};console.log("reservation data",t);const gs=[{value:"all",label:"All Reservations"},{value:"me",label:"My Reservations"},...B.map(l=>({value:l,label:`${l.first_name} ${l.last_name} (${l.family_role||"Family Member"})`}))];return s.jsxs("div",{children:[s.jsxs("div",{className:"bg-white px-4 pt-4",children:[s.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My Reservations"}),(B.length>0||ps)&&s.jsxs("div",{className:"mb-6 max-w-sm",children:[s.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Filter by family member"}),s.jsx(Xs,{className:"w-full text-sm",options:gs,onChange:us,value:_,placeholder:"Select family member",isSearchable:!1,defaultValue:gs[0]})]}),s.jsx("div",{className:"mb-0 flex max-w-fit  text-sm",children:Ps.map(l=>s.jsxs("button",{onClick:()=>Es(l.id),className:`flex items-center gap-2 bg-transparent px-3 py-3 ${L===l.id?"border-b-2 border-primaryBlue":""}`,children:[l.icon(),s.jsx("span",{className:"",children:l.label})]},l.id))})]}),s.jsxs("div",{className:"h-screen px-8",children:[A&&s.jsx(Qs,{}),A?s.jsx(Js,{}):s.jsxs("div",{className:"overflow-x-auto",children:[s.jsxs("table",{className:"w-full min-w-[1024px] ",children:[s.jsx("thead",{children:s.jsx("tr",{children:ys.map((l,S)=>s.jsx("th",{scope:"col",className:"px-6 py-4 text-left text-sm font-medium text-gray-500",children:l.header},S))})}),s.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:t.map((l,S)=>{const O=V(l==null?void 0:l.reservation_updated_at);return s.jsx("tr",{onClick:()=>fs(l),className:"hover:bg-gray-40 cursor-pointer rounded-lg bg-white px-4 py-3 text-gray-500",children:ys.map((o,x)=>{var $,j,p,w,z,cs,y;return o.accessor==""?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>fs(l),children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})})},x):o.accessor==="reservation_status"?s.jsxs("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:[(l==null?void 0:l.booking_type)=="Find Buddy"&&s.jsxs(s.Fragment,{children:[(l==null?void 0:l.num_needed)==(l==null?void 0:l.num_players)&&s.jsx(bs,{title:"Group full"}),(l==null?void 0:l.num_needed)!=(l==null?void 0:l.num_players)&&s.jsx(Cs,{numberOfBuddies:l==null?void 0:l.num_needed})]}),(l==null?void 0:l.booking_status)===c.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Q,{}),s.jsx(X,{timeLeft:O})]})||(l==null?void 0:l.booking_status)===c.SUCCESS&&s.jsx(W,{})||(l==null?void 0:l.booking_status)===c.FAIL&&s.jsx(v,{}),(l==null?void 0:l.booking_status)===c.CANCELLED&&s.jsx(ss,{})]},x):o.mappingExist?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:o.mappings[l[o.accessor]]},x):o.accessor==="type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:($=Us.find(G=>G.value===(l==null?void 0:l.type)))==null?void 0:$.label},x):o.accessor==="date"?s.jsxs("td",{className:"whitespace-nowrap rounded-l-3xl px-6 py-4",children:[Hs((l==null?void 0:l.booking_date)||"")||"--"," "," | "," ",js((l==null?void 0:l.start_time)||"")||"--"," "," - "," ",js((l==null?void 0:l.end_time)||"")||"--"]},x):o.accessor==="booking_type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(l==null?void 0:l.booking_type)||"--"},x):o.accessor==="players"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(j=l==null?void 0:l.booking)!=null&&j.player_ids?`${JSON.parse((p=l==null?void 0:l.booking)==null?void 0:p.player_ids).length} players`:"0 players"},x):o.accessor==="price"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:k((l==null?void 0:l.price)||0)},x):o.accessor==="user"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:!((w=l==null?void 0:l.user)!=null&&w.first_name)||!((z=l==null?void 0:l.user)!=null&&z.last_name)?"--":`${(cs=l==null?void 0:l.user)==null?void 0:cs.first_name} ${(y=l==null?void 0:l.user)==null?void 0:y.last_name}`},x):s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:l[o.accessor]},x)})},S)})})]}),!A&&t.length===0&&s.jsx("div",{className:"w-full px-6 py-4 text-center",children:s.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),s.jsx(Ks,{currentPage:a,pageCount:N,pageSize:h,canPreviousPage:I,canNextPage:u,updatePageSize:l=>{f(l),r()},previousPage:Ds,nextPage:Rs,gotoPage:l=>r()}),s.jsx(ce,{isOpen:!!ns,onClose:()=>ts(null),reservation:ns,clubSports:Ss,club:Ls,onReservationCanceled:()=>{r(1,h,{}),ts(null)}})]})," "]})};export{Ke as default};
