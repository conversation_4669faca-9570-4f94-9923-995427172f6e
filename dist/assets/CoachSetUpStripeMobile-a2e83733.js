import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r,N as m}from"./vendor-851db8c1.js";import{A as p}from"./AuthLayout-69bd697c.js";import{S as n}from"./StripeConnectionStatus-c897521c.js";import{A as s}from"./index-5b566256.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const K=()=>{const{state:o}=r.useContext(s),i=r.useRef(null);return o!=null&&o.loading?null:o!=null&&o.isAuthenticated?t.jsx(t.Fragment,{children:t.jsx(p,{children:t.jsx("div",{className:"mx-auto max-w-[432px] px-5",children:t.jsx(n,{ref:i,successMessage:"Please return to the mobile application.",noConnectionMessage:"Connect your Stripe account to receive payments from your club members. This is required for processing payments on the platform.",autoCheck:!0,onConnectionStatusChange:e=>{console.log("Stripe connection status changed:",e)}})})})}):t.jsx(m,{to:"/coach/login"})};export{K as default};
