import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,b as be}from"./vendor-851db8c1.js";import{d as Je,M as Ce,ak as ls,R as Fe,G as De,e as $e,al as os,b as Z,K as Ge,a3 as We,H as fe,J as ie,E as ye}from"./index-5b566256.js";import{u as is}from"./react-hook-form-687afde5.js";import{S as Ve}from"./react-select-c8303602.js";import{B as Ye}from"./BottomDrawer-a217558a.js";import{H as ds}from"./HistoryComponent-4f475aec.js";import{P as cs}from"./PencilIcon-35185602.js";import{T as ms}from"./TrashIcon-7d213648.js";import{b as Le}from"./@headlessui/react-a5400090.js";function us({title:d,titleId:r,...l},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":r},l),d?s.createElement("title",{id:r},d):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))}const hs=s.forwardRef(us),xs=hs;function Be({isOpen:d,onClose:r,onConfirm:l,eventCount:n,affectedReservations:g=[],isSubmitting:y,type:k="hours"}){if(!d)return null;const B=b=>{if(!b)return"";const[U,G]=b.split(":"),S=parseInt(U),w=S>=12?"PM":"AM";return`${S%12||12}:${G} ${w}`},P=b=>b?new Date(b).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}):"";return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Confirm Changes"}),e.jsx("p",{className:"mb-4 text-gray-600",children:`Changing ${k==="court"?"this court":`these ${k}`} will delete ${n} existing reservation${n!==1?"s":""}.`}),g.length>0&&e.jsxs("div",{className:"mb-6 max-h-64 overflow-y-auto rounded-lg border border-gray-200",children:[e.jsx("div",{className:"bg-gray-50 px-4 py-2",children:e.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Affected Reservations:"})}),e.jsx("div",{className:"divide-y divide-gray-200",children:g.map((b,U)=>e.jsx("div",{className:"px-4 py-3 text-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:b.sport_name}),e.jsx("p",{className:"text-gray-600",children:b.first_name&&b.last_name?`${b.first_name} ${b.last_name}`:b.email})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-gray-900",children:P(b.date)}),e.jsxs("p",{className:"text-gray-600",children:[B(b.start_time)," -"," ",B(b.end_time)]})]})]})},U))})]}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to continue?"}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:r,className:"rounded-lg border border-gray-200 px-6 py-2",disabled:y,children:"Cancel"}),e.jsx(Je,{onClick:l,className:"rounded-lg bg-primaryBlue px-6 py-2 text-white",loading:y,children:"Confirm"})]})]})]})}const Ke=be.forwardRef(({onSubmit:d=()=>{},initialData:r={},club:l={},isOpen:n=!1,onClose:g=()=>{},title:y="Edit club settings",onPrimaryAction:k=()=>{},submitting:B=!1},P)=>{const b=new Ce,{handleSubmit:U}=is(),G=l!=null&&l.times?JSON.parse(l.times):[],[S,w]=s.useState(()=>G.length>0?(console.log("Using parsed times:",G),G):[{from:"",until:""}]),[R,$]=s.useState(!1),[Y,c]=s.useState(0),[u,m]=s.useState([]),[M,L]=s.useState(!1),[v,z]=s.useState(!1),[E,W]=s.useState(null);be.useEffect(()=>{console.log("Current time slots:",S)},[S]);const A=(o,j,t)=>{w(a=>{const h=[...a];return h[o]={...h[o],[j]:t?t.value:""},h})},T=async o=>{try{z(!0);const j=await b.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{times:o.times,days_off:o.days_off},"POST");return j&&!j.error&&j.total_affected>0?(c(j.total_affected),m(j.affected_reservations||[]),W(o),$(!0),!0):!1}catch(j){return console.error("Error checking for affected events:",j),!1}finally{z(!1)}},J=async()=>{L(!0);try{const o={...E,delete_affected_events:!0};await d(o),$(!1),W(null)}catch(o){console.error("Error saving changes:",o)}finally{L(!1)}},K=()=>{$(!1),W(null),c(0),m([])};be.useImperativeHandle(P,()=>({submit:async()=>new Promise(o=>{U(async j=>{const t=Object.entries(q.daysOff).filter(([_,ee])=>ee).map(([_])=>_),a=q.dailyBreaks?[{start:q.breakStartTime,end:q.breakEndTime}]:[],h={times:S,daily_breaks:a,days_off:t};await T(h)||await d(h),o(h)})()})}));const[q,Q]=s.useState({times:S,daysOff:{Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1,Sunday:!1,...l!=null&&l.days_off?JSON.parse(l.days_off).reduce((o,j)=>({...o,[j]:!0}),{}):{}},dailyBreaks:!1,breakStartTime:"09:00:00",breakEndTime:"10:00:00",...r});be.useEffect(()=>{if(l!=null&&l.daily_breaks)try{const o=JSON.parse(l.daily_breaks);o&&o.length>0&&Q(j=>({...j,dailyBreaks:!0,breakStartTime:o[0].start||j.breakStartTime,breakEndTime:o[0].end||j.breakEndTime}))}catch(o){console.error("Error parsing daily breaks:",o)}},[l]);const X=o=>{Q(j=>({...j,daysOff:{...j.daysOff,[o]:!j.daysOff[o]}}))},D=ls().map(o=>({...o,value:o.value+":00"}));console.log("Time options:",D);const p=()=>{w(o=>[...o,{from:"",until:""}])},C=o=>{w(j=>j.filter((t,a)=>a!==o))};return e.jsxs(e.Fragment,{children:[e.jsx(Fe,{isOpen:n,onClose:g,title:y,onPrimaryAction:k,submitting:B||v,children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"General opening hours"}),S.map((o,j)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(Ve,{classNamePrefix:"select",options:D,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},value:(()=>{const t=D.find(a=>a.value===o.from);return console.log(`Finding option for from=${o.from}:`,t),t||null})(),onChange:t=>A(j,"from",t),placeholder:"Select time",isClearable:!0})}),e.jsx("div",{className:"flex-1",children:e.jsx(Ve,{classNamePrefix:"select",components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},options:o.from?D.filter(t=>t.value>o.from):D,value:(()=>{const t=D.find(a=>a.value===o.until);return console.log(`Finding option for until=${o.until}:`,t),t||null})(),onChange:t=>A(j,"until",t),placeholder:"Select time",isDisabled:!o.from,isClearable:!0})})]}),S.length>1&&e.jsx("button",{onClick:()=>C(j),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]},j))]}),e.jsx("button",{onClick:p,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Days off"}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:Object.keys(q.daysOff).map(o=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:q.daysOff[o],onChange:()=>X(o),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-600",children:o})]},o))})]})]})}),e.jsx(Be,{isOpen:R,onClose:K,onConfirm:J,eventCount:Y,affectedReservations:u,isSubmitting:M,type:"hours"})]})});Ke.displayName="ClubSettingsEditForm";const fs=Ke;function ps({initialData:d={},onSubmit:r,setExceptionName:l,exceptionName:n}){return e.jsx("div",{className:"flex flex-col gap-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Exception details"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("label",{className:"mb-2 block text-sm text-gray-600",children:"Name"}),e.jsx("input",{type:"text",value:n,onChange:g=>l(g.target.value),placeholder:"Enter exception name",className:"w-full rounded-xl border border-gray-200 px-3 py-2 text-sm shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"})]})]})})}const gs=new Ce;function Ze({onSubmit:d,initialData:r,onClose:l,isOpen:n,isEdit:g,club:y,sports:k=[],courts:B,title:P,primaryButtonText:b,submitting:U,showFooter:G=!1,onPrimaryAction:S}){var ee,ne,Se;const[w,R]=s.useState({name:(r==null?void 0:r.name)||"",sport_id:Number(r==null?void 0:r.sport_id)||null,type:(r==null?void 0:r.type)||"",sub_type:(r==null?void 0:r.sub_type)||null}),[$,Y]=s.useState(!1),[c,u]=s.useState(!1),[m,M]=s.useState(!1),[L,v]=s.useState(null),[z,E]=s.useState(null),[W,A]=s.useState(!1),[T,J]=s.useState(0),[K,q]=s.useState(null),{dispatch:Q}=s.useContext(De),X=()=>{R({name:"",sport_id:null,type:"",sub_type:null})};s.useEffect(()=>{r?R({name:r.name||"",sport_id:Number(r.sport_id)||null,type:r.type||"",sub_type:r.sub_type||null}):g||X()},[r,g]);const D=s.useCallback(x=>{const{name:H,value:le}=x.target;R(de=>{const ue=H==="sport_id"?Number(le):le;if(de[H]===ue)return de;const ae={...de};return ae[H]=ue,H==="sport_id"&&(ae.type="",ae.sub_type=null),H==="type"&&(ae.sub_type=null),ae})},[]),p=async()=>{M(!1),u(!0);try{const x={court_id:r.id,...w,sport_id:L,type:"",sub_type:null,sport_change_option:z};if(await o(x)){u(!1);return}await d(x),g||X()}catch(x){console.error(x)}finally{u(!1)}},C=()=>{v(null),E(null),M(!1)},o=async x=>{try{await gs.callRawAPI("/v3/api/custom/courtmatchup/club/check-affected-events",{court_id:x.court_id,sport_id:x.sport_id,type:x.type,sub_type:x.sub_type},"POST");const H={affected_events_count:Math.floor(Math.random()*5)};return H.affected_events_count>0?(J(H.affected_events_count),q(x),A(!0),!0):!1}catch(H){return console.error("Error checking for affected events:",H),!1}},j=async()=>{u(!0);try{await d(K),A(!1),g||X()}catch(x){console.error("Error saving changes:",x)}finally{u(!1)}},t=async x=>{if(x.preventDefault(),h.length>0&&!w.type){Z(Q,"Please select a court type",3e3,"error");return}if(I.length>0&&!w.sub_type){Z(Q,"Please select a surface type",3e3,"error");return}if(g&&(r!=null&&r.sport_id)&&w.sport_id!==r.sport_id){v(w.sport_id),M(!0);return}u(!0);try{const H=g?{court_id:r.id,...w}:w;if(g&&await o(H)){u(!1);return}await d(H),g||X()}catch(H){console.error(H)}finally{u(!1)}},a=k.find(x=>x.id===w.sport_id),h=((ee=a==null?void 0:a.sport_types)==null?void 0:ee.filter(x=>x.type))||[],I=((Se=(ne=a==null?void 0:a.sport_types)==null?void 0:ne.find(x=>x.type===w.type))==null?void 0:Se.subtype)||[],_=()=>{var pe,_e;const[x,H]=s.useState(""),le=ce=>{H(ce.target.value)},de=()=>{E(Number(x)),p()},ue=((pe=k.find(ce=>ce.id===(r==null?void 0:r.sport_id)))==null?void 0:pe.name)||"previous sport",ae=((_e=k.find(ce=>ce.id===L))==null?void 0:_e.name)||"new sport";return e.jsxs("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-white p-6",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Change Sport"}),e.jsxs("p",{className:"mb-4 text-gray-600",children:["You are changing the sport for this space from"," ",e.jsx("strong",{children:ue})," to ",e.jsx("strong",{children:ae}),". However, any events for this space will still be listed under the previous sport, including in the daily scheduler."]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"Would you like to:"}),e.jsxs("div",{className:"mb-6 space-y-3",children:[e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"1",checked:x==="1",onChange:le,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["A: Keep these events (events will remain under ",ue,")"]})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"2",checked:x==="2",onChange:le,className:"mt-1 h-4 w-4"}),e.jsx("span",{children:"B: Delete these events"})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"3",checked:x==="3",onChange:le,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["C: Change the sport listed for these events to ",ae]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:C,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:de,disabled:!x,className:`rounded-lg px-4 py-2 text-white ${x?"bg-primaryBlue hover:bg-blue-700":"cursor-not-allowed bg-gray-400"}`,children:"Confirm"})]})]})]})};return e.jsxs("div",{children:[$&&e.jsx($e,{}),e.jsx(Fe,{isOpen:n!==void 0?n:!0,onClose:l,title:P||(g?"Edit court":"Add new court"),showFooter:G,primaryButtonText:b||(g?"Save changes":"Add court"),onPrimaryAction:S,submitting:U||c,children:e.jsx(os,{isLoading:$,children:e.jsxs("form",{onSubmit:t,className:"flex h-full flex-col",children:[e.jsxs("div",{className:"h-full flex-1 space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Court Name"}),e.jsx("input",{type:"text",name:"name",value:w.name,onChange:D,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter court name",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Sport"}),e.jsx("div",{className:"mt-2 space-x-4",children:k.filter(x=>x.status===1).map(x=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"sport_id",value:x.id,checked:w.sport_id===x.id,onChange:D,className:"form-radio"}),e.jsx("span",{className:"ml-2",children:x.name})]},x.id))})]}),h.length>0&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-2 space-x-4",children:h.map(x=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"type",value:x.type,checked:w.type===x.type,onChange:D,className:"form-radio",required:!0}),e.jsx("span",{className:"ml-2",children:x.type})]},x.type))})]}),w.type&&I.length>0&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Sub Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-2 space-x-4",children:I.map(x=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"sub_type",value:x,checked:w.sub_type===x,onChange:D,className:"form-radio",required:!0}),e.jsx("span",{className:"ml-2",children:x})]},x))})]})]}),e.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:l,children:"Cancel"}),e.jsx(Je,{type:"submit",loading:c,className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:g?"Save changes":"Add court"})]})]})})}),m&&e.jsx(_,{}),e.jsx(Be,{isOpen:W,onClose:()=>A(!1),onConfirm:j,eventCount:T,isSubmitting:c,type:"court"})]})}function ze({showTimesAvailableModal:d,setShowTimesAvailableModal:r,selectedCourt:l,setSelectedTimes:n,isSubmitting:g,selectedTimes:y,onSave:k,minTime:B=0,maxTime:P=23,allowMultipleSlots:b=!0,title:U="Times available"}){const[G,S]=s.useState(null),[w,R]=s.useState(!1),[$,Y]=s.useState(null),[c,u]=s.useState(null),[m,M]=s.useState({start:null,current:null}),L=s.useRef(new Map),v=s.useRef(null),z=s.useRef(new Set),E=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],A=(()=>{const t=[];for(let a=0;a<=23;a++)t.push(`${a.toString().padStart(2,"0")}:00:00`),t.push(`${a.toString().padStart(2,"0")}:30:00`);return t})(),T=(t,a)=>{n(h=>h.some(_=>_.time===t&&_.day===a)?h.filter(_=>!(_.time===t&&_.day===a)):[...h,{time:t,day:a}])},J=(t,a)=>{S(null)},K=(t,a)=>{n(h=>h.filter(I=>!(I.time===t&&I.day===a))),S(null)},q=()=>{const t={};return y.forEach(a=>{const h=a.day.toLowerCase();t[h]||(t[h]=[]),t[h].push(a.time)}),Object.entries(t).map(([a,h])=>({day:a,timeslots:[...new Set(h)].sort()}))},Q=t=>{const[a,h]=t.split(":"),I=parseInt(a),_=I>=12?"PM":"AM";let ee=I%12;return ee===0&&(ee=12),`${ee}:${h} ${_}`},X=s.useCallback((t,a)=>{if(!t||!a.start||!a.current)return!1;const h=t.getBoundingClientRect(),I=Math.min(a.start.x,a.current.x),_=Math.max(a.start.x,a.current.x),ee=Math.min(a.start.y,a.current.y),ne=Math.max(a.start.y,a.current.y);return!(h.right<I||h.left>_||h.bottom<ee||h.top>ne)},[]),D=s.useCallback(t=>{v.current&&cancelAnimationFrame(v.current),v.current=requestAnimationFrame(()=>{L.current.forEach((a,h)=>{if(X(a,t)){const[I,_]=h.split("|");y.some(ne=>ne.time===_&&ne.day===I)||T(_,I)}})})},[X,y]),p=(t,a,h)=>{R(!0),Y(t),u(a);const I={start:{x:h.clientX,y:h.clientY},current:{x:h.clientX,y:h.clientY}};M(I),z.current.clear(),T(t,a)},C=s.useCallback(t=>{if(w){t.preventDefault();const a={...m,current:{x:t.clientX,y:t.clientY}};M(a),D(a)}},[w,m,D]),o=s.useCallback(()=>{R(!1),Y(null),u(null),M({start:null,current:null}),z.current.clear(),v.current&&cancelAnimationFrame(v.current)},[]),j=(t,a)=>{w&&(y.some(I=>I.time===t&&I.day===a)||T(t,a))};return s.useEffect(()=>{if(w)return window.addEventListener("mousemove",C),window.addEventListener("mouseup",o),()=>{window.removeEventListener("mousemove",C),window.removeEventListener("mouseup",o)}},[w,C,o]),e.jsx(Ye,{isOpen:d,onClose:()=>r(!1),title:U,onDiscard:()=>{n([]),r(!1)},discardLabel:"Discard",showActions:!0,isSubmitting:g,saveLabel:"Save changes",leftElement:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]}),e.jsx("p",{className:"text-xl",children:l==null?void 0:l.name})]}),onSave:()=>{const t=q();k==null||k(t),r(!1)},children:e.jsxs("div",{className:"w-full select-none",children:[w&&m.start&&m.current&&e.jsx("div",{style:{position:"fixed",left:Math.min(m.start.x,m.current.x),top:Math.min(m.start.y,m.current.y),width:Math.abs(m.current.x-m.start.x),height:Math.abs(m.current.y-m.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),e.jsx("div",{className:"grid grid-cols-7 gap-5",children:E.map(t=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium capitalize",children:t}),e.jsx("div",{className:"space-y-2",children:A.map(a=>{const h=y.some(_=>_.time===a&&_.day===t),I=`${t}-${a}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{ref:_=>{_&&L.current.set(`${t}|${a}`,_)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${h?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":"border-gray-300 hover:border-gray-400"}`,onMouseDown:_=>p(a,t,_),onMouseEnter:()=>j(a,t),onMouseUp:o,onClick:()=>{h?K(a,t):T(a,t)},children:Q(a)}),G===I&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>J(),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>K(a,t),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]},`${t}-${a}`)})})]},t))})]})})}function ys({showExceptionTimeSelector:d,setShowExceptionTimeSelector:r,selectedException:l,setSelectedExceptionTimes:n,selectedExceptionTimes:g,exceptions:y,onSelectException:k,onSave:B,isSubmitting:P}){s.useContext(De);const b=new Ce,[U,G]=s.useState(null),[S,w]=s.useState(!1),[R,$]=s.useState(null),[Y,c]=s.useState(null),u=s.useRef(null),[m,M]=s.useState(!1),[L,v]=s.useState((l==null?void 0:l.name)||""),z=s.useRef(null),[E,W]=s.useState(!1),[A,T]=s.useState(0),[J,K]=s.useState([]),[q,Q]=s.useState(!1),[X,D]=s.useState(null);s.useEffect(()=>{v((l==null?void 0:l.name)||"")},[l]),s.useEffect(()=>{m&&z.current&&z.current.focus()},[m]);const[p,C]=s.useState(()=>(y==null?void 0:y.findIndex(i=>i.name===(l==null?void 0:l.name)))||0),o=()=>{if(p>0){const i=p-1;C(i),k(y[i])}},j=()=>{if(p<y.length-1){const i=p+1;C(i),k(y[i])}},t=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],h=(()=>{const i=[];for(let f=8;f<=22;f++){const V=`${f.toString().padStart(2,"0")}:00:00`;i.push(V)}return i})(),I=(i,f)=>{const V=g.some(O=>O.time===i&&O.day===f);n(V?O=>O.filter(me=>!(me.time===i&&me.day===f)):O=>[...O,{time:i,day:f}])},_=(i,f)=>{n(V=>V.filter(O=>!(O.time===i&&O.day===f))),G(null)},ee=()=>{const i=t.map(f=>{const V=g.filter(O=>O.day===f).map(O=>O.time).sort();return V.length>0?{day:f.toLowerCase(),timeslots:V}:null}).filter(Boolean);return{name:l==null?void 0:l.name,days:i}},ne=i=>{const f=parseInt(i.split(":")[0]);return f<12?`${f}:00 AM`:`${f===12?12:f-12}:00 PM`},Se=i=>{v(i.target.value)},x=()=>{L.trim()&&(l.name=L.trim(),M(!1))},H=i=>{i.key==="Enter"?x():i.key==="Escape"&&(v((l==null?void 0:l.name)||""),M(!1))},le=async i=>{try{Q(!0);const f=await b.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{exceptions:i},"POST");return f&&!f.error&&f.total_affected>0?(T(f.total_affected),K(f.affected_reservations||[]),D(i),W(!0),!0):!1}catch(f){return console.error("Error checking for affected events:",f),!1}finally{Q(!1)}},de=async()=>{try{await B({exceptions:X,delete_affected_events:!0}),W(!1),D(null),r(!1),n([])}catch(i){console.error("Error saving changes:",i)}},ue=()=>{W(!1),D(null),T(0),K([])},ae=e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",onClick:o,disabled:p===0,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:p===0?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",onClick:j,disabled:p===(y==null?void 0:y.length)-1,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:p===(y==null?void 0:y.length)-1?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsx("div",{className:"flex items-center gap-2",children:m?e.jsx("input",{ref:z,type:"text",value:L,onChange:Se,onBlur:x,onKeyDown:H,className:"w-48 rounded-lg border border-primaryBlue bg-white px-3 py-1 text-xl outline-none",placeholder:"Enter exception name"}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-xl",children:L}),e.jsx("button",{onClick:()=>M(!0),className:"rounded-lg p-1 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.7167 7.51667L12.4833 8.28333L4.93333 15.8333H4.16667V15.0667L11.7167 7.51667ZM14.7167 2.5C14.5083 2.5 14.2917 2.58333 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C15.1417 2.575 14.9333 2.5 14.7167 2.5ZM11.7167 5.15833L2.5 14.375V17.5H5.625L14.8417 8.28333L11.7167 5.15833Z",fill:"#868C98"})})})]})})]});s.useEffect(()=>{var i;if(l){const f=((i=l.days)==null?void 0:i.flatMap(V=>V.timeslots.map(O=>({day:V.day.charAt(0).toUpperCase()+V.day.slice(1),time:O}))))||[];n(f)}},[l]);const pe=(i,f)=>{if(!R||!Y)return!1;const[V,O]=R.split("-"),[me,he]=Y.split("-"),we=t.indexOf(f),ke=t.indexOf(V),xe=t.indexOf(me),je=Math.min(ke,xe),ve=Math.max(ke,xe);if(we<je||we>ve)return!1;const Me=h.indexOf(i),Ne=h.indexOf(O),se=h.indexOf(he),oe=Math.min(Ne,se),re=Math.max(Ne,se);return Me>=oe&&Me<=re},_e=(i,f)=>{u.current&&clearTimeout(u.current),$(`${f}-${i}`),c(`${f}-${i}`),u.current=setTimeout(()=>{w(!0)},150)},ce=(i,f)=>{S&&c(`${f}-${i}`)},Ie=()=>{if(u.current&&(clearTimeout(u.current),u.current=null),S){const[i,f]=R.split("-"),[V,O]=Y.split("-"),me=t.indexOf(i),he=t.indexOf(V),we=Math.min(me,he),ke=Math.max(me,he),xe=h.indexOf(f),je=h.indexOf(O),ve=Math.min(xe,je),Me=Math.max(xe,je),Ne=[];for(let se=we;se<=ke;se++){const oe=t[se];for(let re=ve;re<=Me;re++){const Ee=h[re];g.some(Ae=>Ae.time===Ee&&Ae.day===oe)||Ne.push({time:Ee,day:oe})}}n(se=>[...se,...Ne]),w(!1),$(null),c(null)}};return s.useEffect(()=>{const i=()=>{S&&Ie()};return window.addEventListener("mouseup",i),()=>window.removeEventListener("mouseup",i)},[S,R,Y]),e.jsx(Ye,{isOpen:d,onClose:()=>r(!1),title:"Exception times",onDiscard:()=>{n([]),r(!1)},discardLabel:"Discard",showActions:!0,saveLabel:"Save changes",leftElement:ae,onSave:async()=>{const i=ee(),f=y.map(O=>O.name===l.name?i:O);await le(f)||(await B(f),r(!1),n([]))},isSubmitting:P||q,children:e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"grid grid-cols-7 gap-5",children:t.map(i=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium",children:i}),e.jsx("div",{className:"space-y-2",children:h.map(f=>{const V=g==null?void 0:g.some(he=>he.time===f&&he.day===i),O=S&&pe(f,i),me=`${i}-${f}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${V?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":O?"border-primaryBlue bg-[#EBF1FF] bg-opacity-50":"border-gray-300 hover:border-gray-400"}`,onMouseDown:()=>_e(f,i),onMouseEnter:()=>ce(f,i),onClick:()=>{u.current&&(clearTimeout(u.current),u.current=null),S||I(f,i),w(!1),$(null),c(null)},children:ne(f)}),U===me&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{onClick:()=>_(f,i),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})})})]},`${i}-${f}`)})})]},i))}),e.jsx(Be,{isOpen:E,onClose:ue,onConfirm:de,eventCount:A,affectedReservations:J,isSubmitting:P,type:"exception"})]})})}const ws=({exceptions:d,setExceptions:r,selectedException:l,setSelectedException:n,showExceptionTimeSelector:g,setShowExceptionTimeSelector:y,selectedExceptionTimes:k,setSelectedExceptionTimes:B,showDeleteExceptionModal:P,setShowDeleteExceptionModal:b,selectedExceptionIndex:U,setSelectedExceptionIndex:G,deletingException:S,deleteException:w,setShowExceptionEditModal:R,showHowItWorksModal:$,setShowHowItWorksModal:Y,timesSelectorIsSubmitting:c,setTimesSelectorIsSubmitting:u,userRole:m,profileSettings:M,globalDispatch:L,handleSearchException:v})=>{const z=new Ce;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-8 max-w-4xl",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Exceptions"}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",onClick:()=>{n(null),R(!0)},children:"+ Add new"})})]}),e.jsxs("div",{className:"mb-6 flex justify-between",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search",onChange:E=>v(E.target.value),className:"w-full rounded-md border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-600",onClick:()=>Y(!0),children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.16732 7.33398H8.00065L8.00065 10.834M14.1673 8.00065C14.1673 11.4064 11.4064 14.1673 8.00065 14.1673C4.5949 14.1673 1.83398 11.4064 1.83398 8.00065C1.83398 4.5949 4.5949 1.83398 8.00065 1.83398C11.4064 1.83398 14.1673 4.5949 14.1673 8.00065Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.54102 5.33333C7.54102 5.58646 7.74622 5.79167 7.99935 5.79167C8.25248 5.79167 8.45768 5.58646 8.45768 5.33333C8.45768 5.0802 8.25248 4.875 7.99935 4.875C7.74622 4.875 7.54102 5.0802 7.54102 5.33333Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]}),"How it works"]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-2 shadow",children:[e.jsx("div",{className:"px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Exception name"}),e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Days applicable"})]})}),e.jsx("ul",{className:"pb-5",children:Array.isArray(d)&&d.length>0?d.map((E,W)=>{var A;return e.jsx("li",{className:"mx-2 my-3 rounded-xl bg-gray-100 px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-900",children:E.name}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:(A=E.days)==null?void 0:A.map(T=>T.day.slice(0,3).toUpperCase()).join(", ")}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-500",onClick:()=>{var J;n(E);const T=((J=E.days)==null?void 0:J.flatMap(K=>K.timeslots.map(q=>({day:K.day.charAt(0).toUpperCase()+K.day.slice(1),time:q}))))||[];B(T),y(!0)},children:e.jsx(cs,{className:"h-5 w-5"})}),e.jsx("button",{onClick:()=>{G(W),b(!0)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(ms,{className:"h-5 w-5"})})]})]})]})},W)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No exceptions found"})})]})]}),e.jsx(ys,{showExceptionTimeSelector:g,setShowExceptionTimeSelector:y,selectedException:l,setSelectedExceptionTimes:B,selectedExceptionTimes:k,exceptions:d,onSelectException:E=>{var A;n(E);const W=((A=E.days)==null?void 0:A.flatMap(T=>T.timeslots.map(J=>({day:T.day.charAt(0).toUpperCase()+T.day.slice(1),time:J}))))||[];B(W)},isSubmitting:c,onSave:async E=>{var W;u(!0);try{let A,T;Array.isArray(E)?(A={exceptions:E},T=E):(A=E,T=E.exceptions),m==="club"?await z.callRawAPI(`/v3/api/custom/courtmatchup/${m}/profile-edit`,A,"POST"):await z.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${(W=M==null?void 0:M.user)==null?void 0:W.id}`,A,"POST"),r(T),Z(L,"Times updated successfully",3e3,"success")}catch(A){console.error(A),Z(L,"Error updating times",3e3,"error")}finally{u(!1)}}}),e.jsx(Ge,{onClose:()=>b(!1),isOpen:P,onDelete:()=>w(U),message:"Are you sure you want to delete this exception?",loading:S})]})},js=({club:d,courts:r,exceptions:l,setShowEditModal:n,searchQuery:g,setSearchQuery:y,filteredCourts:k,activeDropdown:B,setActiveDropdown:P,dropdownPosition:b,setDropdownPosition:U,setSelectedCourtForEdit:G,setShowEditCourtModal:S,setSelectedCourtForDelete:w,setShowDeleteCourtModal:R,setShowAddCourtModal:$,sports:Y,globalDispatch:c,edit_api:u,fetchSettings:m})=>{var X,D;const[M,L]=s.useState((d==null?void 0:d.allow_user_court_selection)===1),[v,z]=s.useState(!1),[E,W]=s.useState([]),[A,T]=s.useState(!1),J=new Ce,K=localStorage.getItem("user");s.useEffect(()=>{if(r&&r.length>0){const p=r.map(C=>({id:C.id,name:C.name,sport_id:C.sport_id,type:C.type||"",sub_type:C.sub_type||"",allow_reservation:C.allow_reservation!==!1,allow_lesson:C.allow_lesson!==!1,allow_clinic:C.allow_clinic!==!1,allow_buddy:C.allow_buddy!==!1}));L((d==null?void 0:d.allow_user_court_selection)===1),W(p)}},[r]);const q=async()=>{const p=!M;z(!0);try{await J.callRawAPI(u,{allow_user_court_selection:p?1:0},"POST"),await fe(J,{user_id:K,activity_type:ie.court_management,action_type:ie.UPDATE,data:{allow_user_court_selection:p?1:0},club_id:d==null?void 0:d.id,description:`${p?"Enabled":"Disabled"} user court selection`}),L(p),Z(c,`User court selection ${p?"enabled":"disabled"} successfully`,3e3,"success"),m&&m()}catch(C){console.error("Error updating user court selection setting:",C),Z(c,"Error updating setting",3e3,"error")}finally{z(!1)}},Q=async()=>{T(!0);try{const p=E.map(C=>({id:C.id,allow_reservation:C.allow_reservation,allow_lesson:C.allow_lesson,allow_clinic:C.allow_clinic,allow_buddy:C.allow_buddy}));await J.callRawAPI(u,{court_settings:p},"POST"),await fe(J,{user_id:K,activity_type:ie.court_management,action_type:ie.UPDATE,data:p,club_id:d==null?void 0:d.id,description:"Updated court usage settings"}),Z(c,"Court settings saved successfully",3e3,"success"),m&&m()}catch(p){console.error("Error saving court settings:",p),Z(c,"Error saving court settings",3e3,"error")}finally{T(!1)}};return e.jsxs("div",{className:"mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between rounded-lg bg-[#F6F8FA] px-6 py-3",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Club settings"}),e.jsx("button",{className:"text-sm font-medium text-gray-600",onClick:()=>n(!0),children:"Edit"})]}),e.jsxs("div",{className:"flex flex-col divide-y divide-gray-200 p-6",children:[e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"GENERAL OPENING HOURS"}),e.jsx("p",{className:"mt-1",children:d!=null&&d.times&&JSON.parse(d==null?void 0:d.times).length>0?(X=JSON.parse(d==null?void 0:d.times))==null?void 0:X.map(p=>e.jsxs("div",{children:[We(p.from)," -"," ",We(p.until)]},p.from)):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DAYS OFF"}),e.jsx("p",{className:"mt-1",children:d!=null&&d.days_off?(D=JSON.parse(d==null?void 0:d.days_off))==null?void 0:D.join(", "):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"TOTAL COURTS"}),e.jsx("p",{className:"mt-1",children:r!=null&&r.length?r==null?void 0:r.length:"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SCHEDULED EXCEPTIONS"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("p",{className:"mt-1",children:l==null?void 0:l.length})})]}),e.jsxs("div",{className:"py-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"ALLOW USERS TO SELECT COURT"}),e.jsx("button",{onClick:q,disabled:v,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${M?"bg-primaryBlue":"bg-gray-200"} ${v?"cursor-not-allowed opacity-50":""}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${M?"translate-x-6":"translate-x-1"}`})})]}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"When enabled, users can select their preferred court during reservation."})]})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-6 shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Courts"}),e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:bg-gray-400",onClick:Q,disabled:A,children:A?"Saving...":"Save Court Settings"})]}),e.jsx("div",{className:"mb-4 flex gap-4",children:e.jsxs("div",{className:"relative w-96",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"Search by name, sport, type, sub-type...",className:"w-full rounded-lg border border-gray-300 px-4 py-2 pl-10 text-sm focus:border-blue-500 focus:outline-none",value:g,onChange:p=>y(p.target.value)})]})}),e.jsx("div",{className:"overflow-x-auto overflow-y-hidden",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"px-4 pb-4",children:"Name"}),e.jsx("th",{className:"px-4 pb-4",children:"Sport"}),e.jsx("th",{className:"px-4 pb-4",children:"Type"}),e.jsx("th",{className:"px-4 pb-4",children:"Sub-type"}),e.jsx("th",{className:"px-4 pb-4"})]})}),(k==null?void 0:k.length)>0?k==null?void 0:k.map(p=>{var C;return e.jsx("tbody",{children:e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-gray-100 px-4 py-3",children:p.name?p.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:p.sport_id?(C=Y.find(o=>o.id==p.sport_id))==null?void 0:C.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:p.type||"--"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:p.sub_type||"--"}),e.jsx("td",{className:"rounded-r-xl bg-gray-100 px-4 py-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:o=>{const j=o.currentTarget.getBoundingClientRect(),t=o.currentTarget.closest("table").getBoundingClientRect(),a=j.bottom>t.bottom-100;U(a?"top":"bottom"),P(B===p.id?null:p.id)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(xs,{className:"h-5 w-5"})}),B===p.id&&e.jsx("div",{className:`absolute right-0 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 ${b==="top"?"bottom-full mb-1":"top-full mt-1"}`,children:e.jsxs("div",{className:"py-1",role:"menu",children:[e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{G(p),S(!0),P(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20756L13.5768 2.67181C13.9022 2.34638 14.4298 2.34637 14.7553 2.67181L17.3268 5.2433C17.6522 5.56874 17.6522 6.09638 17.3268 6.42181L14.791 8.95756M11.041 5.20756L2.53509 13.7135C2.37881 13.8698 2.29102 14.0817 2.29102 14.3027V17.7076H5.69584C5.91685 17.7076 6.12881 17.6198 6.28509 17.4635L14.791 8.95756M11.041 5.20756L14.791 8.95756",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Edit"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{w(p.id),R(!0),P(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Delete"]})]})})]})})]})},p.id)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No courts found"})]})}),e.jsx("button",{className:"mt-6 rounded-md px-4 py-2 text-sm font-medium text-black underline",onClick:()=>$(!0),children:"+ Add court"})]})]})},vs=({courts:d=[],sports:r=[],edit_api:l,globalDispatch:n,fetchSettings:g,club:y})=>{const[k]=s.useState(!1),[B,P]=s.useState([]),[b,U]=s.useState(!1),[G,S]=s.useState(!1),w=new Ce,R=localStorage.getItem("user");s.useEffect(()=>{if(d&&d.length>0){const c=d.map(u=>{const m=u.court_settings||{};return{id:u.id,name:u.name,sport_id:u.sport_id,type:u.type||"",sub_type:u.sub_type||"",min_booking_time:m.min_booking_time||30,allow_reservation:m.allow_reservation!==!1,allow_lesson:m.allow_lesson!==!1,allow_clinic:m.allow_clinic!==!1,allow_buddy:m.allow_buddy!==!1,court_settings:m}});P(c)}},[d]);const $=(c,u,m)=>{P(M=>M.map(L=>L.id===c?{...L,[u]:m}:L)),S(!0)},Y=async()=>{U(!0);try{const c=B.map(u=>({court_id:u.id,court_settings:{min_booking_time:u.min_booking_time,allow_reservation:u.allow_reservation,allow_lesson:u.allow_lesson,allow_clinic:u.allow_clinic,allow_buddy:u.allow_buddy}}));await w.callRawAPI(l,{courts:c},"POST"),await fe(w,{user_id:R,activity_type:ye.court_management,action_type:ie.UPDATE,data:c,club_id:y==null?void 0:y.id,description:"Updated court settings"}),Z(n,"Court settings saved successfully",3e3,"success"),S(!1),g&&g()}catch(c){console.error("Error saving court settings:",c),Z(n,"Error saving court settings",3e3,"error")}finally{U(!1)}};return e.jsxs("div",{className:"mt-8",children:[k&&e.jsx($e,{}),e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Court Settings"})}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:'Configure minimum booking time and allowed activities for each court. Make your changes and click "Save Changes" to apply them.'})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Court Name"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Sport"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Min. Booking Time"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Reservation"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Lesson"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Clinic"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Find-a-Buddy"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:B.length>0?B.map(c=>{var u;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:c.name})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:((u=r.find(m=>m.id===c.sport_id))==null?void 0:u.name)||"N/A"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"number",min:"30",max:"1440",step:"30",value:c.min_booking_time,onChange:m=>{const M=parseInt(m.target.value,10);isNaN(M)||M<30||M>1440||$(c.id,"min_booking_time",M)},className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"minutes"})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:c.allow_reservation,onChange:m=>{$(c.id,"allow_reservation",m)},className:`${c.allow_reservation?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${c.allow_reservation?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:c.allow_lesson,onChange:m=>{$(c.id,"allow_lesson",m)},className:`${c.allow_lesson?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${c.allow_lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:c.allow_clinic,onChange:m=>{$(c.id,"allow_clinic",m)},className:`${c.allow_clinic?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${c.allow_clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:c.allow_buddy,onChange:m=>{$(c.id,"allow_buddy",m)},className:`${c.allow_buddy?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${c.allow_buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})})]},c.id)}):e.jsx("tr",{children:e.jsx("td",{colSpan:"7",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No courts found. Please add courts in the General Settings tab."})})})]})}),B.length>0&&e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("p",{className:"text-xs text-gray-500",children:"* Minimum booking time can be set between 30 minutes and 24 hours (1440 minutes)."}),e.jsx("button",{onClick:Y,disabled:b,className:"inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:b?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"-ml-1 mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):"Save Changes"})]})]})]})},te=new Ce;function Ls({profileSettings:d,fetchSettings:r,sports:l=[],club:n,courts:g,edit_api:y}){const[k,B]=s.useState("general-settings"),[P,b]=s.useState(null),[U,G]=s.useState("bottom"),[S,w]=s.useState(!1),[R,$]=s.useState(!1),[Y,c]=s.useState(!1),[u,m]=s.useState(null),[M,L]=s.useState(!1),{dispatch:v}=be.useContext(De),[z,E]=s.useState(!1),[W,A]=s.useState(!1),[T,J]=s.useState(!1),[K,q]=s.useState(null),[Q,X]=s.useState([]),[D,p]=s.useState(""),[C,o]=s.useState(!1),[j,t]=s.useState(!1),[a,h]=s.useState(null),[I,_]=s.useState(!1),[ee,ne]=s.useState(!1),[Se,x]=s.useState([]),[H,le]=s.useState(!1),[de,ue]=s.useState(""),[ae,pe]=s.useState(!1),[_e,ce]=s.useState(null),[Ie,i]=s.useState(!1),[f,V]=s.useState(!1),[O,me]=s.useState(null),[he,we]=s.useState(!1),[ke,xe]=s.useState(!1),[je,ve]=s.useState(null),[Me]=s.useState(!1),Ne=localStorage.getItem("role"),[se,oe]=s.useState(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]),re=localStorage.getItem("user");console.log("courts",g),s.useEffect(()=>{const N=F=>{P&&!F.target.closest(".relative")&&b(null)};return document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}},[P]),s.useEffect(()=>{oe(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[])},[n==null?void 0:n.exceptions]),be.useEffect(()=>{v({type:"SETPATH",payload:{path:"court-management"}})},[]);const Ee=async N=>{_(!0);const F=se.filter((ge,Te)=>Te!==N);try{await te.callRawAPI(y,{exceptions:F},"POST"),await fe(te,{user_id:re,activity_type:ye.court_management,action_type:ie.DELETE,data:F,club_id:n==null?void 0:n.id,description:"Deleted court exception"}),oe(F),t(!1),_(!1),Z(v,"Exception deleted successfully")}catch{_(!1)}},Ae=N=>{if(N===""){oe(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]);return}const F=se.filter(ge=>ge.name.toLowerCase().includes(N.toLowerCase()));oe(F)},qe=async()=>{we(!0);try{te.setTable("club_court"),await te.callRestAPI({id:je},"DELETE"),await fe(te,{user_id:re,activity_type:ye.court_management,action_type:ie.DELETE,data:je,club_id:n==null?void 0:n.id,description:"Deleted court"})}catch(N){console.log(N)}finally{we(!1),ve(null),xe(!1),r()}};console.log(se);const Xe=()=>{switch(k){case"exceptions":return e.jsx(ws,{exceptions:se,setExceptions:oe,selectedException:u,setSelectedException:m,showExceptionTimeSelector:ee,setShowExceptionTimeSelector:ne,selectedExceptionTimes:Se,setSelectedExceptionTimes:x,showDeleteExceptionModal:j,setShowDeleteExceptionModal:t,selectedExceptionIndex:a,setSelectedExceptionIndex:h,deletingException:I,deleteException:Ee,setShowExceptionEditModal:c,showHowItWorksModal:S,setShowHowItWorksModal:w,timesSelectorIsSubmitting:H,setTimesSelectorIsSubmitting:le,userRole:Ne,profileSettings:d,globalDispatch:v,handleSearchException:Ae});case"general-settings":return e.jsx(js,{club:n,courts:g,exceptions:se,setShowEditModal:$,searchQuery:de,setSearchQuery:ue,filteredCourts:Qe,activeDropdown:P,setActiveDropdown:b,dropdownPosition:U,setDropdownPosition:G,setSelectedCourtForEdit:ce,setShowEditCourtModal:pe,setSelectedCourtForDelete:ve,setShowDeleteCourtModal:xe,setShowAddCourtModal:L,sports:l,globalDispatch:v,edit_api:y,fetchSettings:r});case"court-settings":return e.jsx(vs,{courts:g,sports:l,edit_api:y,globalDispatch:v,fetchSettings:r,club:n});default:return null}},Qe=g==null?void 0:g.filter(N=>{var ge,Te,Re,He,Ue;const F=de.toLowerCase();return((ge=N.name)==null?void 0:ge.toLowerCase().includes(F))||((Re=(Te=l.find(rs=>rs.id==N.sport_id))==null?void 0:Te.name)==null?void 0:Re.toLowerCase().includes(F))||((He=N.type)==null?void 0:He.toLowerCase().includes(F))||((Ue=N.sub_type)==null?void 0:Ue.toLowerCase().includes(F))}),es=()=>S?e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-lg rounded-2xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx("h2",{className:"text-lg font-semibold",children:"How it works"})}),e.jsx("div",{className:"mb-6 space-y-4 text-sm text-gray-600",children:e.jsxs("p",{children:[e.jsxs("ol",{className:"list-inside list-decimal space-y-2",children:[e.jsx("li",{children:"You can define which hours the exceptions take place here and the exception will automatically be added everywhere in the schedule where you defined the hours for it."}),e.jsx("li",{children:"When adding an event in the daily scheduler, the club can specify these schedule exception names and define the date/time/repeating details."})]}),e.jsx("p",{className:"mt-2",children:'Separately, you can also create custom event types in the daily scheduler by using the event type "other" and defining the name of the event.'})]})})]}),e.jsx("div",{className:"flex justify-end border-t border-gray-200 p-6",children:e.jsx("button",{onClick:()=>w(!1),className:"rounded-xl bg-[#2B5F2B] px-5 py-3 text-sm text-white",children:"Close"})})]})}):null,Oe=be.useRef(null),ss=async()=>{Oe.current&&await Oe.current.submit()},ts=async N=>{try{E(!0),await te.callRawAPI(y,N,"POST"),await fe(te,{user_id:re,activity_type:ye.court_management,action_type:ie.UPDATE,data:N,club_id:n==null?void 0:n.id,description:"Updated club settings"}),$(!1),Z(v,"Settings saved successfully",3e3,"success"),r()}catch(F){console.error("Error saving settings:",F),Z(v,"Error saving settings",3e3,"error")}finally{E(!1)}},Pe=async()=>{if(o(!0),!D.length){Z(v,"Please enter a name",3e3,"error");return}const N={name:D,days:[]},F=[...se,N];try{await te.callRawAPI(y,{exceptions:F},"POST"),await fe(te,{user_id:re,activity_type:ye.court_management,action_type:ie.CREATE,data:N,club_id:n==null?void 0:n.id,description:"Added new court exception"}),oe(F),Z(v,"Exception added successfully",3e3,"success"),c(!1),m(N),ne(!0),p("")}catch(ge){console.error(ge),Z(v,"Error adding exception",3e3,"error")}finally{o(!1)}},ns=async N=>{A(!0);try{const F={courts:[N]};await te.callRawAPI(y,F,"POST"),await fe(te,{user_id:re,activity_type:ye.court_management,action_type:ie.CREATE,data:N,club_id:n==null?void 0:n.id,description:"Added new court"}),L(!1),Z(v,"Court added successfully",3e3,"success"),r()}catch(F){console.error(F)}finally{A(!1)}},as=async N=>{i(!0);try{await te.callRawAPI(y,{courts:[N]},"POST"),await fe(te,{user_id:re,activity_type:ye.court_management,action_type:ie.UPDATE,data:N,club_id:n==null?void 0:n.id,description:"Updated court"}),Z(v,"Court updated successfully",3e3,"success"),pe(!1),r()}catch(F){console.error(F),Z(v,"Error updating court",3e3,"error")}finally{i(!1)}};return e.jsxs("div",{className:"",children:[Me&&e.jsx($e,{}),e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200",children:[e.jsx("nav",{className:"-mb-px flex space-x-8",children:["General settings","Court settings","Exceptions"].map(N=>e.jsx("button",{className:`
                whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium
                ${k===N.toLowerCase().replace(" ","-")?"border-primaryBlue text-primaryBlue":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}
              `,onClick:()=>B(N.toLowerCase().replace(" ","-")),children:N},N))}),e.jsx(ds,{title:"Court Management History",emptyMessage:"No court management history found",activityType:ye.court_management})]}),Xe(),e.jsx(es,{}),e.jsx(fs,{ref:Oe,club:n,onSubmit:ts,isOpen:R,onClose:()=>$(!1),title:"Edit club settings",onPrimaryAction:ss,submitting:z}),e.jsx(Fe,{isOpen:Y,onClose:()=>{c(!1),m(null)},title:u?"Edit exception":"Add exception",onPrimaryAction:Pe,primaryButtonText:u?"Save changes":"Add exception",submitting:C,children:e.jsx(ps,{initialData:u,onSubmit:Pe,setExceptionName:p,exceptionName:D})}),e.jsx(Ze,{profileSettings:d,club:n,sports:l,courts:g,onSubmit:ns,onClose:()=>L(!1),isEdit:!1,isOpen:M,title:"Add new court",showFooter:!1}),e.jsx(ze,{selectedCourt:K,selectedTimes:Q,setSelectedTimes:X,showTimesAvailableModal:T,setShowTimesAvailableModal:J}),e.jsx(Ze,{onSubmit:as,isEdit:!0,initialData:_e,sports:l,courts:g,profileSettings:d,onClose:()=>{pe(!1),ce(null)},isOpen:ae,title:"Edit court",primaryButtonText:"Save changes",submitting:Ie,showFooter:!1}),e.jsx(ze,{showTimesAvailableModal:f,setShowTimesAvailableModal:V,selectedCourt:O,selectedTimes:Q,setSelectedTimes:X,onSave:async N=>{try{const F={courts:[{court_id:O.id,availability:N}]};await te.callRawAPI(y,F,"POST"),Z(v,"Times updated successfully",3e3,"success"),r()}catch(F){console.error(F),Z(v,"Error updating times",3e3,"error")}}}),e.jsx(Ge,{isOpen:ke,onClose:()=>{xe(!1),ve(null)},title:"Delete court",loading:he,onDelete:qe,message:"Are you sure you want to delete this court?"})]})}export{Ls as C};
