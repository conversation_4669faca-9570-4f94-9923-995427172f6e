import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as o}from"./vendor-851db8c1.js";import{u as k,R as D,e as H,M,T as $}from"./index-5b566256.js";import{f as E}from"./date-fns-07266b7d.js";const A=({isOpen:y,onClose:j,title:v="History",historyData:g=[],columns:p=[],emptyMessage:w="No history found",getData:c,pageSize:x})=>{const f=localStorage.getItem("user"),{club:i}=k(),[n,d]=o.useState(""),[l,b]=o.useState(""),[u,m]=o.useState(""),_=()=>{let t=[];if(u&&t.push(`courtmatchup_activity_logs.description,cs,${u}`),n){const r=n;t.push(`courtmatchup_activity_logs.create_at,gt,${r}`)}if(l){const r=l;t.push(`courtmatchup_activity_logs.create_at,lt,${r}`)}c(1,x,t)},N=t=>{const r=t.target.value.trim();m(r);let s=[];if(r&&s.push(`courtmatchup_activity_logs.description,cs,${r}`),n){const a=n;s.push(`courtmatchup_activity_logs.create_at,ge,${a}`)}if(l){const a=l;s.push(`courtmatchup_activity_logs.create_at,le,${a}`)}c(1,x,s)},h=()=>{d(""),b(""),m(""),c(1,x,[])};return e.jsx(D,{isOpen:y,onClose:j,title:v,showFooter:!1,className:"overflow-hidden",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"relative w-full",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{className:"h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"})})}),e.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-5 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search history...",value:u,onChange:t=>N(t)})]}),e.jsxs("div",{className:"flex flex-wrap gap-3",children:[e.jsxs("div",{className:"flex flex-1 flex-col gap-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),e.jsx("input",{type:"date",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:n,onChange:t=>{d(t.target.value)}})]}),e.jsxs("div",{className:"flex flex-1 flex-col gap-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Date"}),e.jsx("input",{type:"date",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",value:l,onChange:t=>{b(t.target.value)}})]}),e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsx("button",{className:"rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:_,children:"Apply Filters"}),e.jsx("button",{className:"rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:h,children:"Clear Filters"})]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full min-w-[600px] table-auto divide-y divide-gray-200",children:[e.jsx("thead",{children:e.jsx("tr",{children:p.map((t,r)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:t.header},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:g.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:p.length,className:"px-6 py-8 text-center text-sm text-gray-500",children:w})}):g.map((t,r)=>e.jsx("tr",{className:"hover:bg-gray-50",children:p.map((s,a)=>{var C,S;return s.accessor==="date"||s.accessor==="create_at"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500",children:E(new Date(t[s.accessor]),"MM/dd/yyyy HH:mm")},a):s.accessor==="action_type"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500",children:t[s.accessor]==1?"Create":t[s.accessor]==2?"Update":"Delete"},a):s.accessor==="user_id"?t.user_id==f?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500",children:"You"},a):t.user_id==(i==null?void 0:i.user_id)?e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500",children:"Club Admin"},a):e.jsxs("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500",children:[(C=t.user)==null?void 0:C.first_name," ",(S=t.user)==null?void 0:S.last_name]},a):e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm text-gray-500",children:t[s.accessor]||"N/A"},a)})},r))})]})})]})})},F=A;new M;let R=new $;function K({title:y,emptyMessage:j,activityType:v}){const[g,p]=o.useState([]),[w,c]=o.useState(!1),[x,f]=o.useState(!1),[i,n]=o.useState(10),{club:d}=k(),[l,b]=o.useState([{header:"Date",accessor:"create_at"},{header:"Action",accessor:"action_type"},{header:"User",accessor:"user_id"},{header:"Description",accessor:"description"}]),u=async(m,_,N=[])=>{f(!0);try{const h=await R.getPaginate("activity_logs",{page:m,limit:_,size:i,filter:[...N,`courtmatchup_activity_logs.club_id,eq,${d==null?void 0:d.id}`,`courtmatchup_activity_logs.activity_type,cs,${v}`],join:["clubs|club_id","user|user_id"]});p(h.list),c(!0)}catch(h){console.error("Error fetching history data:",h)}finally{f(!1)}};return e.jsxs("div",{children:[x&&e.jsx(H,{}),e.jsxs("button",{onClick:()=>u(1,i),className:"inline-flex items-center gap-2 rounded-lg border border-gray-200 bg-transparent px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50",children:[e.jsx("span",{children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10 6.45703V9.9987L12.9167 12.9154",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M2.29102 3.95703V7.29036H5.62435",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M2.70898 12.5707C3.76873 15.5646 6.62818 17.7096 9.98935 17.7096C14.2528 17.7096 17.709 14.2585 17.709 10.0013C17.709 5.74411 14.2528 2.29297 9.98935 2.29297C6.79072 2.29297 4.04646 4.23552 2.87521 7.00362",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),"History"]}),e.jsx(F,{isOpen:w,onClose:()=>c(!1),title:y,historyData:g,columns:l,emptyMessage:j,pageSize:i,getData:u})]})}export{K as H};
