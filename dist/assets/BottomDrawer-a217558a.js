import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as f}from"./vendor-851db8c1.js";import{d as p,N as b}from"./index-5b566256.js";const y=({isOpen:t,onClose:s,title:o,children:a,onSave:n,onDiscard:i,onSecondaryAction:d,saveLabel:c="Save",discardLabel:x="Discard",secondaryActionLabel:l,showActions:r=!1,leftElement:m=null,isSubmitting:u=!1})=>(f.useEffect(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),t?e.jsxs("div",{className:"fixed left-0 top-0 z-[99999] h-screen w-full",children:[e.jsx("div",{className:"absolute left-0 top-0 h-full w-full bg-gray-500/75 transition-opacity duration-300",onClick:s}),e.jsx("div",{className:"absolute bottom-0 left-0 h-[95vh] w-full transform overflow-auto rounded-t-xl bg-[#F6F8FA] transition-transform duration-300 ease-out",children:e.jsxs("div",{className:"flex-1  pb-6",children:[e.jsxs("div",{className:"sticky top-0 z-50 mb-4 flex items-center justify-between border-b bg-white px-4 py-5",children:[e.jsx("div",{className:"flex items-center",children:m}),e.jsx("h3",{className:"text-center text-lg font-semibold capitalize leading-6 text-gray-900",children:o}),e.jsxs("div",{className:"flex items-center gap-2",children:[r&&l&&e.jsx("button",{type:"button",className:"text-sm text-gray-600 hover:text-gray-800",onClick:d,children:l}),r&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:"rounded-lg border px-3 py-2 text-sm text-gray-500 hover:text-gray-700",onClick:i,children:x}),e.jsx(p,{loading:u,type:"button",className:"rounded-lg bg-primaryBlue px-3 py-2 text-sm font-medium text-white",onClick:n,children:c})]}),e.jsxs("button",{type:"button",className:"rounded-md bg-white p-1 text-gray-400 hover:text-gray-500",onClick:s,children:[e.jsx("span",{className:"sr-only",children:"Close"}),e.jsx(b,{size:24})]})]})]}),e.jsx("div",{className:"overflow-y-auto bg-[#F6F8FA] px-4 pb-6",children:a})]})})]}):null),j=y;export{j as B};
