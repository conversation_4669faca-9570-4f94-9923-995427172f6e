import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as m,f as E}from"./vendor-851db8c1.js";import{u as I}from"./react-hook-form-687afde5.js";import{o as A}from"./yup-2824f222.js";import{c as k,a as r}from"./yup-54691517.js";import{G as D,A as P,d as R,M as C,b as F,t as B}from"./index-5b566256.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as o}from"./MkdInput-829a7fd3.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const ve=({setSidebar:j})=>{var h,f,g,N;const{dispatch:p}=m.useContext(D),_=k({sport_id:r(),type:r(),ntrp:r(),reservation_id:r(),num_players:r(),num_needed:r(),need_coach:r(),notes:r(),player_ids:r(),date:r(),start_time:r(),end_time:r()}).required(),{dispatch:w}=m.useContext(P),[b,M]=m.useState({}),[y,c]=m.useState(!1),S=E(),{register:s,handleSubmit:v,setError:x,setValue:O,formState:{errors:t}}=I({resolver:A(_)});m.useState([]);const T=async a=>{let u=new C;c(!0);try{for(let i in b){let d=new FormData;d.append("file",b[i].file);let n=await u.uploadImage(d);a[i]=n.url}u.setTable("buddy");const l=await u.callRestAPI({sport_id:a.sport_id,type:a.type,ntrp:a.ntrp,reservation_id:a.reservation_id,num_players:a.num_players,num_needed:a.num_needed,need_coach:a.need_coach,notes:a.notes,player_ids:a.player_ids,date:a.date,start_time:a.start_time,end_time:a.end_time},"POST");if(!l.error)F(p,"Added"),S("/club/buddy"),j(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(l.validation){const i=Object.keys(l.validation);for(let d=0;d<i.length;d++){const n=i[d];x(n,{type:"manual",message:l.validation[n]})}}c(!1)}catch(l){c(!1),console.log("Error",l),x("sport_id",{type:"manual",message:l.message}),B(w,l.message)}};return m.useEffect(()=>{p({type:"SETPATH",payload:{path:"buddy"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Buddy"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:v(T),children:[e.jsx(o,{type:"number",page:"add",name:"sport_id",errors:t,label:"Sport Id",placeholder:"Sport Id",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"type",errors:t,label:"Type",placeholder:"Type",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"ntrp",errors:t,label:"Ntrp",placeholder:"Ntrp",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"reservation_id",errors:t,label:"Reservation Id",placeholder:"Reservation Id",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"num_players",errors:t,label:"Num Players",placeholder:"Num Players",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"num_needed",errors:t,label:"Num Needed",placeholder:"Num Needed",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"need_coach",errors:t,label:"Need Coach",placeholder:"Need Coach",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"notes",children:"Notes"}),e.jsx("textarea",{placeholder:"Notes",...s("notes"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=t.notes)!=null&&h.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(f=t.notes)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"player_ids",children:"Player Ids"}),e.jsx("textarea",{placeholder:"Player Ids",...s("player_ids"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(g=t.player_ids)!=null&&g.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(N=t.player_ids)==null?void 0:N.message})]}),e.jsx(o,{type:"date",page:"add",name:"date",errors:t,label:"Date",placeholder:"Date",register:s,className:""}),e.jsx(o,{page:"add",name:"start_time",errors:t,label:"Start Time",placeholder:"Start Time",register:s,className:""}),e.jsx(o,{page:"add",name:"end_time",errors:t,label:"End Time",placeholder:"End Time",register:s,className:""}),e.jsx(R,{type:"submit",loading:y,disabled:y,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ve as default};
