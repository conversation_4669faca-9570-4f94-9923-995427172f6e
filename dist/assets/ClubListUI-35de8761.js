import{j as m}from"./@nivo/heatmap-ba1ecfff.js";import{C as n}from"./ClubUI-0952407b.js";import{r as o}from"./vendor-851db8c1.js";import{u as f,M as d}from"./index-5b566256.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./SportList-4e787c50.js";import"./PlusIcon-7e8d14d7.js";import"./PencilIcon-35185602.js";import"./TrashIcon-aaaccaf2.js";import"./InformationCircleIcon-d35f3488.js";import"./SplashScreenPagePreview-56a830db.js";import"./BottomDrawer-a217558a.js";import"./ImageCropModal-87d74440.js";import"./react-image-crop-1f5038af.js";import"./index.esm-09a3a6b8.js";import"./react-icons-51bc3cff.js";import"./index.esm-b72032a7.js";import"./AuthLayout-69bd697c.js";import"./MembershipCard-37c1a488.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./HistoryComponent-4f475aec.js";import"./date-fns-07266b7d.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let g=new d;function dt(){const[p,s]=o.useState(null),[b,r]=o.useState(!1),{club:i,fetchClubData:e,courts:a,sports:c,pricing:l}=f(),u=async()=>{r(!0);try{const t=await g.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");s(t==null?void 0:t.model)}catch(t){console.log(t)}finally{r(!1)}};return o.useEffect(()=>{u()},[]),m.jsx("div",{children:m.jsx(n,{selectedClub:i,profileSettings:p,fetchSettings:e,club:i,courts:a,sports:c,pricing:l})})}export{dt as default};
