import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{G as Oe,u as Ie,e as Le,d as ae,aK as oe,K as We,M as De,b as x}from"./index-5b566256.js";import{T as re}from"./TimeSlotGrid-3140c36d.js";import{h as Ee}from"./moment-a9aaa855.js";import{r as d}from"./vendor-851db8c1.js";import{t as Me}from"./index.esm-09a3a6b8.js";import{M as le}from"./react-tooltip-7a26650a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let N=new De;const A=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],D=(c,u)=>{if(!u||u.length===0)return!0;const[y,p]=c.split(":"),w=parseInt(y)*60+parseInt(p);return u.some(S=>{const[b,z]=S.from.split(":"),[T,g]=S.until.split(":"),_=parseInt(b)*60+parseInt(z),k=parseInt(T)*60+parseInt(g);return w>=_&&w<k})},E=(c,u)=>!u||u.length===0?!1:u.includes(c),P=(c,u)=>{if(!u||u.length===0)return[];const y=c.toLowerCase();return u.reduce((p,w)=>{const S=w.days.find(b=>b.day===y);return S&&p.push({name:w.name,timeslots:S.timeslots}),p},[])},Y=(c,u)=>{if(!u||u.length===0)return!1;const y=c.includes(":00",5)?c:`${c}:00`;return u.some(p=>p.timeslots.includes(y))},F=(c,u)=>{if(!u||u.length===0)return null;const y=c.includes(":00",5)?c:`${c}:00`,p=u.find(w=>w.timeslots.includes(y));return p?p.name:null};function wt(){const{dispatch:c}=d.useContext(Oe),[u,y]=d.useState(0),[p,w]=d.useState(Ee()),[S,b]=d.useState(!1),[z,T]=d.useState(!1),[g,_]=d.useState("calendar"),[k,C]=d.useState([]),[O,I]=d.useState([]),[B,L]=d.useState(null),[G,R]=d.useState(null),[ne,ce]=d.useState(null),[V,v]=d.useState(!0),[ue,H]=d.useState(!1),[Z,de]=d.useState(window.innerWidth<768),{club:j}=Ie(),[m,fe]=d.useState({times:[],daysOff:[],exceptions:[]}),[J,$]=d.useState([]),[K,W]=d.useState(!1),M=()=>{if(!k||!B)return!1;const e=Q(),s=Array.isArray(B)?B.filter(i=>i.timeslots&&i.timeslots.length>0):[];if(e.length===0&&s.length>0||e.length>0&&s.length===0||e.length!==s.length)return!0;const a={};s.forEach(i=>{a[i.day.toLowerCase()]=[...i.timeslots].sort()});for(const i of e){const o=i.day.toLowerCase(),l=a[o];if(!l||i.timeslots.length!==l.length)return!0;const n=[...i.timeslots].sort();for(let r=0;r<n.length;r++){const h=n[r].replace(/:00$/,""),f=l[r].replace(/:00$/,"");if(h!==f)return!0}}return!1},q=()=>{if(!O||!G)return!1;const e=X(),s=Array.isArray(G)?G.filter(i=>i.timeslots&&i.timeslots.length>0):[];if(e.length===0&&s.length>0||e.length>0&&s.length===0||e.length!==s.length)return!0;const a={};s.forEach(i=>{a[i.day.toLowerCase()]=[...i.timeslots].sort()});for(const i of e){const o=i.day.toLowerCase(),l=a[o];if(!l||i.timeslots.length!==l.length)return!0;const n=[...i.timeslots].sort();for(let r=0;r<n.length;r++){const h=n[r].replace(/:00$/,""),f=l[r].replace(/:00$/,"");if(h!==f)return!0}}return!1},U=()=>p.clone().startOf("week").format("YYYY-MM-DD");async function me(){v(!0);try{const e=await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),s=JSON.parse(e.availability),a=JSON.parse(e.default_availability),i=e.week_specific_availability?JSON.parse(e.week_specific_availability):[],o=A.map(n=>({day:n.toLowerCase(),timeslots:[]})),l=A.map(n=>({day:n.toLowerCase(),timeslots:[]}));s&&Array.isArray(s)&&s.length>0&&s.forEach(n=>{const r=o.findIndex(h=>h.day===n.day.toLowerCase());r!==-1&&(o[r].timeslots=n.timeslots)}),a&&Array.isArray(a)&&a.forEach(n=>{const r=l.findIndex(h=>h.day===n.day.toLowerCase());r!==-1&&(l[r].timeslots=n.timeslots)}),I(l),R(a||[]),$(i),ce(e),he(o,i)}catch(e){console.error(e)}finally{v(!1)}}const he=(e,s=null)=>{const a=s||J,i=U(),o=a.find(l=>l.weekId===i);if(console.log("loadAvailabilityForCurrentWeek - Week specific data found:",o),o&&o.availability){const l=JSON.parse(JSON.stringify(o.availability));console.log("Setting custom schedule for week:",i,l),C(l),L(l),W(!0)}else{const l=JSON.parse(JSON.stringify(e));C(l),L(l),W(!1)}},pe=(e,s)=>{if(E(s,m.daysOff)){x(c,`${s} is a club day off`,3e3,"error");return}if(!D(e,m.times)){x(c,"This time is outside club hours",3e3,"error");return}const a=P(s,m.exceptions);if(Y(e,a)){const i=F(e,a);x(c,`This time is marked as "${i||"Exception"}"`,3e3,"warning")}C(i=>{const o=i.map(n=>{if(n.day===s.toLowerCase()){const r=e.replace(":00","");if(!n.timeslots.some(f=>f===e||f===r))return{...n,timeslots:[...n.timeslots,e].sort()}}return n});return o.some(n=>n.day===s.toLowerCase())||o.push({day:s.toLowerCase(),timeslots:[e]}),o})},xe=(e,s)=>{if(E(s,m.daysOff)){x(c,`${s} is a club day off`,3e3,"error");return}if(!D(e,m.times)){x(c,"This time is outside club hours",3e3,"error");return}const a=P(s,m.exceptions);if(Y(e,a)){const i=F(e,a);x(c,`This time is marked as "${i||"Exception"}"`,3e3,"warning")}I(i=>i.map(o=>{if(o.day===s.toLowerCase()){const l=e.replace(":00","");if(!o.timeslots.some(r=>r===e||r===l))return{...o,timeslots:[...o.timeslots,e].sort()}}return o}))},ye=(e,s)=>{C(a=>a.map(i=>{if(i.day===s.toLowerCase()){const o=i.timeslots.filter(l=>l!==e&&l!==e.replace(":00",""));return o.length===0?null:{...i,timeslots:o}}return i}).filter(Boolean))},we=(e,s)=>{I(a=>a.map(i=>i.day===s.toLowerCase()?{...i,timeslots:i.timeslots.filter(o=>o!==e&&o!==e.replace(":00",""))}:i))},Q=()=>k?k.filter(e=>e.timeslots.length>0):[],X=()=>O?O.filter(e=>e.timeslots.length>0):[],ge=async()=>{try{b(!0);const e=Q(),s=U();if(console.log("Saving changes for week:",s,e),!M()){console.log("No changes detected, skipping save"),b(!1);return}const a=[...J],i=a.findIndex(r=>r.weekId===s);i!==-1?a[i].availability=e:a.push({weekId:s,availability:e}),a.sort((r,h)=>r.weekId.localeCompare(h.weekId)),console.log("Sending updated week-specific availability to server:",a),await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{week_specific_availability:a},"POST");const o=await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),l=o.week_specific_availability?JSON.parse(o.week_specific_availability):[];console.log("Received updated week-specific availability from server:",l),$(l);const n=l.find(r=>r.weekId===s);if(console.log("Current week data after save:",n),n&&n.availability){const r=JSON.parse(JSON.stringify(n.availability));console.log("Setting current week data to:",r),C(r),L(r),W(!0)}else console.error("Error: Week data not found after saving!");x(c,"Week availability updated successfully",3e3,"success"),b(!1)}catch(e){console.error("Error in onSaveChanges:",e),b(!1),x(c,"Failed to update week availability",3e3,"error")}},ve=async()=>{try{T(!0);const e=X();if(console.log("Saving default availability:",e),!q()){console.log("No changes detected in default availability, skipping save"),T(!1);return}await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{default_availability:e},"POST");const s=await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),a=JSON.parse(s.default_availability);console.log("Received updated default availability from server:",a);const i=A.map(o=>({day:o.toLowerCase(),timeslots:[]}));a&&Array.isArray(a)&&a.forEach(o=>{const l=i.findIndex(n=>n.day===o.day.toLowerCase());l!==-1&&(i[l].timeslots=[...o.timeslots])}),I(i),R(a||[]),x(c,"Default availability updated successfully",3e3,"success"),T(!1)}catch(e){console.error("Error in onSaveDefaultChanges:",e),T(!1),x(c,"Failed to update default availability",3e3,"error")}},ee=async e=>{try{v(!0);const s=e.clone().startOf("week").format("YYYY-MM-DD");console.log(`Switching to week: ${s}`);const a=await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),i=JSON.parse(a.default_availability),o=a.week_specific_availability?JSON.parse(a.week_specific_availability):[];console.log("Fetched week-specific availability:",o),$(o);const l=A.map(r=>({day:r.toLowerCase(),timeslots:[]}));i&&Array.isArray(i)&&i.forEach(r=>{const h=l.findIndex(f=>f.day===r.day.toLowerCase());h!==-1&&(l[h].timeslots=[...r.timeslots])}),I(l),R(i||[]);const n=o.find(r=>r.weekId===s);if(console.log("Week specific data found:",n),n&&n.availability){const r=JSON.parse(JSON.stringify(n.availability));console.log("Found custom schedule for week:",s,r),C(r),L(r),W(!0)}else{console.log("No custom schedule found for week:",s,"Using default schedule");const r=JSON.parse(JSON.stringify(l));C(r),L(r),W(!1)}v(!1)}catch(s){console.error("Error in handleWeekSwitch:",s),v(!1),x(c,"Failed to load availability data",3e3,"error")}},be=async()=>{if(u>0){if(M()&&!window.confirm("You have unsaved changes. Are you sure you want to switch weeks?"))return;const e=u-1;y(e);const s=p.clone().subtract(1,"week");w(s),await ee(s)}},ke=async()=>{if(M()&&!window.confirm("You have unsaved changes. Are you sure you want to switch weeks?"))return;const e=u+1;y(e);const s=p.clone().add(1,"week");w(s),await ee(s)},te=()=>{const e=p.clone().startOf("week"),s=p.clone().endOf("week"),a=`${e.format("MMM D")} - ${s.format("MMM D")}`;return u===0?`This week (${a})`:u===1?`Next week (${a})`:`${u} weeks from now (${a})`},Ce=(e,s)=>{const a=k==null?void 0:k.find(o=>o.day===s.toLowerCase());if(!a)return!1;const i=e.replace(":00","");return a.timeslots.some(o=>o===e||o.replace(":00","")===i)},je=(e,s)=>{const a=O==null?void 0:O.find(o=>o.day===s.toLowerCase());if(!a)return!1;const i=e.replace(":00","");return a.timeslots.some(o=>o===e||o.replace(":00","")===i)};d.useEffect(()=>{if(j){const e=j.times?JSON.parse(j.times):[],s=j.days_off?JSON.parse(j.days_off):[],a=j.exceptions?JSON.parse(j.exceptions):[];fe({times:e,daysOff:s,exceptions:a})}},[j]),d.useEffect(()=>{console.log("Mobile view state updated:",Z)},[Z]),d.useEffect(()=>{me()},[]),d.useEffect(()=>{const e=()=>{de(window.innerWidth<768)};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]);const Ne=()=>{H(!0)},Se=()=>{H(!1)},Ae=async()=>{if(ne){v(!0),H(!1);try{const e=U();console.log("Resetting week to defaults:",e);const s=Array.isArray(J)?J:[];if(!s.some(f=>f.weekId===e)){console.log("This week doesn't have custom availability, nothing to reset"),v(!1);return}const i=s.filter(f=>f.weekId!==e);console.log("Removing custom schedule for week:",e),console.log("Updated week-specific availability:",i),await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{week_specific_availability:i},"POST");const o=await N.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),l=JSON.parse(o.default_availability),n=o.week_specific_availability?JSON.parse(o.week_specific_availability):[];console.log("Fetched updated data from server:",{default_availability:l,week_specific_availability:n}),$(n);const r=A.map(f=>({day:f.toLowerCase(),timeslots:[]}));l&&Array.isArray(l)&&l.forEach(f=>{const ie=r.findIndex(Te=>Te.day===f.day.toLowerCase());ie!==-1&&(r[ie].timeslots=[...f.timeslots])}),I(JSON.parse(JSON.stringify(r))),R(l||[]);const h=JSON.parse(JSON.stringify(r));C(h),L(h),W(!1),x(c,"Availability reset to defaults successfully",3e3,"success"),v(!1)}catch(e){console.error("Error in handleResetToDefaults:",e),v(!1),x(c,"Failed to reset availability to defaults",3e3,"error")}}},se=e=>{g==="calendar"&&M()&&K?window.confirm("You have unsaved changes. Are you sure you want to switch tabs?")&&_(e):_(e)};return t.jsxs("div",{className:"min-h-screen bg-gray-50 p-3 sm:p-4 md:p-6",children:[V&&t.jsx(Le,{}),t.jsxs("div",{className:"w-full",children:[t.jsxs("div",{className:"mb-2 flex flex-wrap items-start justify-between gap-2 sm:flex-nowrap sm:items-center",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("h1",{className:"text-xl font-semibold text-gray-900 sm:text-2xl",children:"My Availability"}),t.jsx("div",{"data-tooltip-id":"availability-help",className:"cursor-pointer text-gray-400 hover:text-gray-600",children:t.jsx(Me,{className:"h-5 w-5"})}),t.jsx(le,{id:"availability-help",place:"right",className:"z-50 max-w-md rounded-lg border border-gray-200 bg-white p-3 text-gray-800 shadow-lg",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px",opacity:1},children:t.jsxs("div",{className:"text-sm",children:[t.jsx("p",{className:"mb-2",children:"Your availability determines the times users can book lessons with you. Each week, the availability shown in the Calendar tab matches the Default tab."}),t.jsx("p",{className:"mb-2",children:"If you want to change your schedule for just one week, update it in the Calendar tab. Your availability will revert to the default next week."}),t.jsx("p",{className:"mb-2",children:"If you need a more permanent change, update your availability in the Default tab."}),t.jsx("p",{children:"If you modify your schedule in the Calendar tab but want to reset it to your default availability, you can click 'Reset to Default'."})]})})]}),K&&g==="calendar"&&t.jsx("div",{className:"rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-700",children:"Custom schedule"})]}),t.jsxs("div",{className:`mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center ${g==="calendar"?"sm:justify-between":"sm:justify-end"}`,children:[g==="calendar"&&t.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center",children:[t.jsx("div",{className:"w-full overflow-x-auto sm:w-auto",children:t.jsx("div",{className:"w-fit max-w-lg rounded-xl bg-white p-1",children:t.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[t.jsx("button",{onClick:be,className:`rounded-xl bg-white p-2 text-gray-600 ${u===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,"aria-label":"Previous week",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:w-24",children:t.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),t.jsx("div",{className:"text-sm font-medium sm:text-lg",children:te()}),t.jsx("button",{onClick:ke,className:"rounded-xl bg-white p-2 text-gray-600 hover:text-gray-800","aria-label":"Next week",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:w-24",children:t.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})})}),t.jsxs("div",{children:[t.jsxs("button",{onClick:Ne,className:"flex items-center gap-1 rounded-lg bg-white p-2 text-sm text-gray-600 hover:text-gray-800",children:[t.jsx("span",{children:"Reset to default"}),t.jsx("span",{"data-tooltip-id":"reset-help",children:t.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M7.16683 7.3335H8.00016L8.00016 10.8335M14.1668 8.00016C14.1668 11.4059 11.4059 14.1668 8.00016 14.1668C4.59441 14.1668 1.8335 11.4059 1.8335 8.00016C1.8335 4.59441 4.59441 1.8335 8.00016 1.8335C11.4059 1.8335 14.1668 4.59441 14.1668 8.00016Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M7.5415 5.33333C7.5415 5.58646 7.74671 5.79167 7.99984 5.79167C8.25297 5.79167 8.45817 5.58646 8.45817 5.33333C8.45817 5.0802 8.25297 4.875 7.99984 4.875C7.74671 4.875 7.5415 5.0802 7.5415 5.33333Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]})})]}),t.jsx(le,{id:"reset-help",place:"top",className:"z-50 max-w-md rounded-lg border border-gray-200 bg-white p-3 text-gray-800 shadow-lg",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px",opacity:1},children:t.jsx("div",{className:"text-sm",children:t.jsx("p",{children:"Reset this week's availability to match your default availability settings."})})})]})]}),t.jsx("div",{className:"flex items-center justify-end",children:t.jsxs("div",{className:"flex w-full divide-x-2 rounded-lg border border-gray-200 sm:w-auto",children:[t.jsx("button",{onClick:()=>se("calendar"),className:`flex-1 rounded-l-lg px-3 py-2 text-sm sm:flex-none sm:px-4 ${g==="calendar"?"bg-white font-medium text-black":"bg-gray-100 text-gray-500"} hover:bg-gray-50`,children:"Calendar"}),t.jsx("button",{onClick:()=>se("defaults"),className:`flex-1 rounded-r-lg px-3 py-2 text-sm sm:flex-none sm:px-4 ${g==="defaults"?"bg-white font-medium text-black":"bg-gray-100 text-gray-500"} hover:bg-gray-50`,children:"Defaults"})]})})]}),g==="calendar"&&t.jsxs(t.Fragment,{children:[M()&&t.jsx("div",{className:"mb-4 flex justify-center sm:justify-start",children:t.jsx(ae,{onClick:ge,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white sm:w-auto",loading:S,children:"Save changes"})}),t.jsxs("div",{children:[K&&t.jsx("div",{className:"mb-4 rounded-lg bg-blue-50 p-3 text-sm",children:t.jsxs("div",{className:"flex flex-col items-start sm:flex-row sm:items-center",children:[t.jsx("svg",{className:"mb-2 h-5 w-5 text-blue-500 sm:mb-0 sm:mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),t.jsxs("p",{children:["You're viewing a customized schedule for ",te(),". Any changes you make will only apply to this week."]})]})}),t.jsx("div",{className:"overflow-x-auto pb-4",children:t.jsx(re,{days:A,isSelected:Ce,handleTimeSelect:pe,handleDeleteTime:ye,renderTimeSlotContent:(e,s)=>{if(E(s,m.daysOff))return t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});if(!D(e.value,m.times))return t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});const a=P(s,m.exceptions);if(Y(e.value,a)){const i=F(e.value,a);return t.jsxs("div",{className:"absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500",children:[t.jsx(oe,{className:"mr-1 hidden sm:inline"}),t.jsx("span",{className:"truncate",children:i||"Exception"})]})}return null},disableTimeSlot:(e,s)=>!!(E(s,m.daysOff)||!D(e.value,m.times))})})]})]}),g==="defaults"&&t.jsxs("div",{children:[q()&&t.jsx("div",{className:"mb-4 flex justify-center sm:justify-start",children:t.jsx(ae,{onClick:ve,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white sm:w-auto",loading:z,children:"Save default changes"})}),t.jsx("div",{className:"mb-4 rounded-lg bg-yellow-50 p-3 text-sm",children:t.jsxs("div",{className:"flex flex-col items-start sm:flex-row sm:items-center",children:[t.jsx("svg",{className:"mb-2 h-5 w-5 text-yellow-500 sm:mb-0 sm:mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),t.jsx("p",{children:"Changes made here will apply to all future weeks unless overridden with a custom weekly schedule."})]})}),t.jsx("div",{className:"overflow-x-auto pb-4",children:t.jsx(re,{days:A,isSelected:je,handleTimeSelect:xe,handleDeleteTime:we,renderTimeSlotContent:(e,s)=>{if(E(s,m.daysOff))return t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});if(!D(e.value,m.times))return t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});const a=P(s,m.exceptions);if(Y(e.value,a)){const i=F(e.value,a);return t.jsxs("div",{className:"absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500",children:[t.jsx(oe,{className:"mr-1 hidden sm:inline"}),t.jsx("span",{className:"truncate",children:i||"Exception"})]})}return null},disableTimeSlot:(e,s)=>!!(E(s,m.daysOff)||!D(e.value,m.times))})})]})]}),t.jsx(We,{isOpen:ue,onClose:Se,onDelete:Ae,title:"Reset to Default",message:"Are you sure you want to reset your current week's availability to match your default settings? Any customizations you've made for this week will be lost.",buttonText:"Yes, Reset",loading:V})]})}export{wt as default};
