import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as q,G as K,e as Q,d as Z,b as C,A as oe,u as ce,m as ne,t as ee}from"./index-5b566256.js";import{r as n,b as de,f as me}from"./vendor-851db8c1.js";import{A as pe}from"./AuthLayout-69bd697c.js";import{u as X}from"./react-hook-form-687afde5.js";import"./LoadingOverlay-87926629.js";import{b as se}from"./index.esm-9c6194ba.js";import{M as te}from"./react-tooltip-7a26650a.js";import{T as xe}from"./TimeSlotGrid-3140c36d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const ue=new q;function he({onNext:w,setValue:a,isSubmitting:S,defaultValues:l}){var h;const[k,y]=n.useState(!1),{register:_,handleSubmit:u,formState:{errors:v},setValue:m,watch:p}=X({defaultValues:{hourly_rate:"",bio:"",photo:null}}),{dispatch:f}=n.useContext(K);n.useEffect(()=>{l&&(m("photo",l.photo),m("hourly_rate",l.hourly_rate),m("bio",l.bio))},[l]);const E=async r=>{try{let i=new FormData;i.append("file",r);const o=await ue.uploadImage(i);if(!(o!=null&&o.url))throw new Error("Upload failed");return o.url}catch(i){return console.error("Upload error:",i),C(f,"Failed to upload file. Please try again.",3e3,"error"),null}},L=async r=>{const i=r.target.files[0];if(console.log("uploading image"),y(!0),i.size>5*1024*1024){C(f,"File size should be less than 5MB",3e3,"error");return}try{const o=await E(i);m("photo",o)}catch(o){console.error("Image upload error:",o),C(f,"Failed to upload image",3e3,"error")}finally{y(!1)}},d=async r=>{try{if(!r.bio){C(f,"Please fill in all required fields",3e3,"error");return}let i=null;const o={hourly_rate:r.hourly_rate,bio:r.bio,photo:r.photo};console.log("finalData",o),a("hourly_rate",r.hourly_rate),a("bio",r.bio),a("photo",r.photo),w&&await w(o)}catch(i){console.error("Submit error:",i),C(f,"Failed to submit form. Please try again.",3e3,"error")}};return e.jsxs("div",{className:"mx-auto w-full max-w-xl flex-1 items-start justify-center",children:[k&&e.jsx(Q,{}),e.jsx("h1",{className:"mb-8 text-center text-2xl font-semibold",children:"Set up your profile"}),e.jsxs("form",{onSubmit:u(d),className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start justify-start gap-4",children:[e.jsx("div",{className:"relative mb-2 h-16 w-16",children:e.jsx("img",{src:p("photo")||"/default-avatar.png",alt:"Profile",className:"h-full w-full rounded-full object-cover"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"mb-1 text-sm font-medium",children:"Upload Image"}),e.jsx("p",{className:"mb-2 text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"}),e.jsxs("label",{className:"mt-2 cursor-pointer rounded-lg border border-gray-300 bg-white px-4 py-1.5 text-sm hover:bg-gray-50",children:[p("photo")?"Change":"Upload",e.jsx("input",{type:"file",className:"hidden",accept:"image/png,image/jpeg",onChange:L})]}),v.photo&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:v.photo.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium",children:"Bio"}),e.jsx("textarea",{rows:5,className:`w-full resize-none rounded-lg border ${v.bio?"border-red-500":"border-gray-300"} px-4 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-green-500`,..._("bio",{required:"Bio is required",minLength:{value:50,message:"Bio should be at least 50 characters"},maxLength:{value:500,message:"Bio should not exceed 500 characters"}})}),v.bio&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:v.bio.message}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:[((h=p("bio"))==null?void 0:h.length)||0,"/500 characters"]})]}),e.jsx(Z,{type:"submit",loading:S,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})]})}const z=new q;function ge({onNext:w,onBack:a,setValue:S,defaultValues:l,isSubmitting:k,clubSports:y,refetchCoachProfile:_}){var G,U;const[u,v]=n.useState({sport_id:"",type:"",sub_type:"",price:""}),[m,p]=n.useState({}),[f,E]=n.useState(!1),[L,d]=n.useState(!1),{dispatch:h}=n.useContext(K),r=(y==null?void 0:y.filter(s=>s.status===1))||[],i=s=>{var P,j,g;const c={};s.sport_id||(c.sport_id="Sport is required");const b=r.find(A=>A.id===parseInt(s.sport_id)),N=(P=b==null?void 0:b.sport_types)==null?void 0:P.some(A=>A.type&&A.type.trim()!=="");N&&!s.type&&(c.type="Type is required");let B=!1;if(N&&s.type&&s.type!=="All"){const A=(j=b==null?void 0:b.sport_types)==null?void 0:j.find(M=>M.type===s.type);B=(g=A==null?void 0:A.subtype)==null?void 0:g.some(M=>M&&M.trim()!==""),B&&!s.sub_type&&(c.sub_type="Sub-type is required")}return s.price||(c.price="Price is required"),s.price&&Number(s.price)<=0&&(c.price="Price must be greater than 0"),s.type==="All"&&(delete c.type,s.sub_type==="All"&&delete c.sub_type),c},o=s=>{const{name:c,value:b}=s.target;v(N=>({...N,[c]:b})),p(N=>({...N,[c]:""}))},F=async()=>{var B,P,j;const s={...u},c=r.find(g=>g.id===parseInt(u.sport_id));if(!((B=c==null?void 0:c.sport_types)==null?void 0:B.some(g=>g.type&&g.type.trim()!=="")))s.type="",s.sub_type="";else if(s.type&&s.type!=="All"){const g=(P=c==null?void 0:c.sport_types)==null?void 0:P.find(M=>M.type===s.type);((j=g==null?void 0:g.subtype)==null?void 0:j.some(M=>M&&M.trim()!==""))||(s.sub_type="")}const N=i(s);if(Object.keys(N).length>0){p(N);return}d(!0);try{z.setTable("coach_sports"),await z.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[s]},"POST"),v({sport_id:"",type:"",sub_type:"",price:""}),await _(),C(h,"Sport added successfully",3e3,"success")}catch(g){console.log(g),C(h,g.message,3e3,"error")}finally{d(!1)}},R=async s=>{E(!0);try{z.setTable("coach_sports"),await z.callRestAPI({id:s},"DELETE"),await _(),C(h,"Sport deleted successfully",3e3,"success")}catch(c){console.log(c),C(h,c.message,3e3,"error")}finally{E(!1)}},Y=async()=>{if(!(l!=null&&l.sports)||(l==null?void 0:l.sports.length)===0){C(h,"Please add at least one sport",3e3,"error");return}w&&await w()},I=r.find(s=>s.id===parseInt(u.sport_id));return e.jsxs("div",{className:"mx-auto w-full max-w-xl flex-1 items-start justify-center",children:[f&&e.jsx(Q,{}),e.jsx("h1",{className:"mb-8 text-center text-2xl font-semibold",children:"What sports are you coaching?"}),e.jsxs("div",{className:"space-y-6",children:[(l==null?void 0:l.sports.length)>0&&e.jsxs("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Added Sports"}),e.jsx("ul",{className:"divide-y divide-gray-200",children:l==null?void 0:l.sports.map((s,c)=>{var b;return e.jsxs("li",{className:"flex items-center justify-between py-3 transition-colors hover:bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(b=r.find(N=>N.id===parseInt(s.sport_id)))==null?void 0:b.name})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("span",{className:"font-medium",children:s.type})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sub-type"}),e.jsx("span",{className:"font-medium",children:s.sub_type})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("span",{className:"font-medium",children:["$",s.price]})]})]}),e.jsx("button",{onClick:()=>R(s.id),className:"ml-4 text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49737 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49737 4.97796Z",fill:"#868C98"})})})]},c)})})]}),e.jsxs("div",{className:"rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Add New Sport"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("select",{name:"sport_id",value:u.sport_id,onChange:o,className:`w-full rounded-lg border ${m.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),r.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),m.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.sport_id})]}),I&&e.jsxs(e.Fragment,{children:[I.sport_types.some(s=>s.type&&s.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type"}),e.jsx(se,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-type-tooltip",size:16}),e.jsx(te,{id:"sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"type",value:u.type,onChange:o,className:`w-full rounded-lg border ${m.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Type"}),I.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),I.sport_types.map((s,c)=>s.type&&s.type.trim()!==""&&e.jsx("option",{value:s.type,children:s.type},c))]}),m.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.type})]}):null,u.type&&I.sport_types.some(s=>s.type===u.type&&s.subtype&&s.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(se,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-subtype-tooltip",size:16}),e.jsx(te,{id:"sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:u.sub_type,onChange:o,className:`w-full rounded-lg border ${m.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),u.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((G=I.sport_types.find(s=>s.type===u.type))==null?void 0:G.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(U=I.sport_types.find(s=>s.type===u.type))==null?void 0:U.subtype.filter(s=>s&&s.trim()!=="").map((s,c)=>e.jsx("option",{value:s,children:s},c))]})]}),m.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.sub_type})]}),e.jsxs("div",{children:[e.jsx("input",{type:"number",name:"price",value:u.price,onChange:o,placeholder:"Enter price",className:`w-full rounded-lg border ${m.price?"border-red-500":"border-gray-300"} p-2`}),m.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.price})]}),e.jsx("button",{type:"button",onClick:F,disabled:L,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:L?"Adding Sport...":"Add Sport"})]})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Z,{type:"button",onClick:a,className:"w-full rounded-xl border border-gray-300 bg-white py-3 text-gray-700 hover:bg-gray-50",children:"Back"}),e.jsx(Z,{type:"button",onClick:Y,loading:k,className:"w-full rounded-xl bg-primaryGreen py-3 text-white disabled:opacity-50",children:"Continue"})]})]})]})}const ye=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];function fe({onNext:w,isSubmitting:a,selectedTimeSlot:S,setSelectedTimeSlot:l,originalAvailability:k,setOriginalAvailability:y,setValue:_,defaultValues:u}){n.useState({}),X();const v=(d,h)=>{const r=S==null?void 0:S.find(o=>o.day===h.toLowerCase());if(!r)return!1;const i=d.replace(":00","");return r.timeslots.some(o=>o===d||o.replace(":00","")===i)},m=(d,h)=>{l(r=>r.map(i=>{if(i.day===h.toLowerCase()){const o=d.replace(":00","");if(!i.timeslots.some(R=>R===d||R===o))return{...i,timeslots:[...i.timeslots,d].sort()}}return i}))},p=(d,h)=>{l(r=>r.map(i=>i.day===h.toLowerCase()?{...i,timeslots:i.timeslots.filter(o=>o!==d&&o!==d.replace(":00",""))}:i))},f=()=>S.filter(d=>d.timeslots.length>0),E=async()=>{try{const d=f();_("availability",d),await w()}catch(d){console.error("Error saving working hours:",d)}},L=async()=>{_("availability",[]),await w()};return e.jsxs("div",{className:"mx-auto w-full max-w-7xl px-4",children:[e.jsx("h1",{className:"mb-8 text-center text-2xl font-semibold",children:"Set your working hours"}),e.jsxs("div",{children:[e.jsx(xe,{days:ye,isSelected:v,handleTimeSelect:m,handleDeleteTime:p}),e.jsxs("div",{className:"mt-8 flex justify-center gap-4",children:[e.jsx(Z,{type:"button",onClick:L,className:"rounded-lg border border-gray-300 px-6 py-2 hover:bg-gray-50",children:"Skip"}),e.jsx(Z,{type:"submit",onClick:E,loading:a,disabled:f().length===0,className:"rounded-lg bg-primaryGreen px-6 py-2 text-white hover:bg-primaryGreen/80 disabled:opacity-50",children:"Continue"})]})]})]})}const re=new q;function be({onNext:w,stripeConnectionData:a,isSubmitting:S,setStripeConnectionData:l}){const[k]=de.useState(S||!1),[y,_]=n.useState(!1),[u,v]=n.useState(!1),[m,p]=n.useState(!1),f=localStorage.getItem("role"),E=async()=>{try{const r=await re.callRawAPI(`/v3/api/custom/courtmatchup/${f}/stripe/account/verify`,{},"POST");return l&&l(r),r}catch(r){return console.error("Error checking Stripe connection:",r),!1}},L=async()=>{_(!0);try{const r=await re.callRawAPI(`/v3/api/custom/courtmatchup/${f}/stripe/onboarding`,{},"POST");r&&r.url&&window.open(r.url,"_blank")}catch(r){console.error("Error connecting to Stripe:",r)}_(!1)};n.useEffect(()=>{if(y===!1){const r=setTimeout(()=>{E()},2e3);return()=>clearTimeout(r)}},[y]);const d=async()=>{v(!0),await w(),v(!1)},h=async()=>{p(!0);try{await w({skip_payment:!0})}finally{p(!1)}};return e.jsx("div",{className:"mx-auto w-fit",children:e.jsx("div",{className:"flex flex-col bg-white pb-7",children:e.jsxs("section",{className:"flex w-[432px] max-w-full flex-col justify-center",children:[e.jsx("div",{className:"flex w-full max-w-[432px] flex-col self-center max-md:max-w-full",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_26852)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_26852)"}),e.jsxs("g",{filter:"url(#filter0_d_397_26852)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M47.3125 34.4999H47V42.7054C47.6877 42.8975 48.2531 43.4195 48.4868 44.1207L48.9035 45.3707C49.3756 46.7872 48.3213 48.2499 46.8282 48.2499H27.1718C25.6787 48.2499 24.6244 46.7872 25.0965 45.3707L25.5132 44.1207C25.7469 43.4195 26.3123 42.8975 27 42.7054V34.4999H26.6875C25.4794 34.4999 24.5 33.5206 24.5 32.3124V31.7352C24.5 30.9099 24.9645 30.1549 25.7011 29.7828L36.0136 24.5729C36.6339 24.2596 37.3661 24.2596 37.9864 24.5729L48.2989 29.7828C49.0355 30.1549 49.5 30.9099 49.5 31.7352V32.3124C49.5 33.5206 48.5206 34.4999 47.3125 34.4999ZM42 34.4999H45.125V42.6249H42V34.4999ZM32 42.6249H28.875V34.4999H32V42.6249ZM33.875 42.6249V34.4999H40.125V42.6249H33.875Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_26852",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_26852"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_26852",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"w-full text-center text-2xl font-medium leading-none text-gray-950 max-md:max-w-full",children:"Connect stripe"})]})}),e.jsxs("div",{className:"mt-10 flex w-full flex-col gap-4 self-center max-md:max-w-full",children:[((a==null?void 0:a.complete)||(a==null?void 0:a.details_submitted))&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:a!=null&&a.complete?"Stripe account connected":"Stripe account details submitted"})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Account ID"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:a.account_id})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${a.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${a.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${a.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("div",{className:"mt-2 text-sm text-gray-600",children:a!=null&&a.complete?"You can now receive payments from your club members.":"Your Stripe account details have been submitted and are pending approval. You can continue with the setup process."}),e.jsx(Z,{onClick:d,className:"mt-4 w-full rounded-xl bg-primaryGreen px-4 py-3 text-sm font-medium text-white",loading:k||u,children:k?"Processing...":"Continue"})]}),!(a!=null&&a.complete||a!=null&&a.details_submitted)&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"No Stripe account connected"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to receive payments from your clients. This is required for processing payments on the platform."}),a&&e.jsxs("div",{className:"mt-3 grid grid-cols-1 gap-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${a.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${a.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${a.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("button",{onClick:L,className:"w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white",disabled:y||m,children:y?"Connecting...":"Connect Stripe Account"}),e.jsxs("div",{className:"mt-4 text-center",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"- OR -"}),e.jsx("button",{onClick:h,className:"w-full rounded-xl border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50",disabled:y||m,children:m?"Processing...":"Will not be paid through Court Matchup"})]})]})]})]})})})}const W=new q,je=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];function ls(){const w=me(),{state:a,dispatch:S}=n.useContext(oe),[l,k]=n.useState(0),[y,_]=n.useState(!1),{dispatch:u,state:v}=n.useContext(K),{triggerRefetch:m}=ce(),[p,f]=n.useState(null),[E,L]=n.useState(null),[d,h]=n.useState(!1),[r,i]=n.useState(null),[o,F]=n.useState(null),[R,Y]=n.useState([]),[I,G]=n.useState(!1),[U,s]=n.useState(null),[c,b]=n.useState(!0),N=localStorage.getItem("role"),B=t=>{var T,x;return!t||!t.bio?0:!((T=t.sports)!=null&&T.length)||!t.sports?1:(x=t.availability)!=null&&x.length?!t.account_details||JSON.stringify(t.account_details).length?3:4:2},{register:P,setValue:j,formState:{errors:g},watch:A,getValues:M}=X({defaultValues:{hourly_rate:"",bio:"",photo:null,sports:[],availability:[],account_details:[]}}),ae=async()=>{b(!0);try{const t=await W.callRawAPI(`/v3/api/custom/courtmatchup/${N}/stripe/account/verify`,{},"POST");s(t)}catch(t){return console.error("Error checking Stripe connection:",t),!1}finally{b(!1)}},J=async()=>{G(!0);try{const t=await W.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");if(await ae(),(t==null?void 0:t.completed)==1){w("/coach/dashboard");return}const T=await W.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${t.club_id}`,{},"GET");L(t),f(T.model),Y(T.sports),j("hourly_rate",(t==null?void 0:t.hourly_rate)||""),j("bio",(t==null?void 0:t.bio)||""),j("photo",(t==null?void 0:t.photo)||"");const x=JSON.parse(t.availability),H=je.map($=>({day:$.toLowerCase(),timeslots:[]}));return j("availability",H),j("account_details",(t==null?void 0:t.account_details)||[]),j("sports",(t==null?void 0:t.sports)||[]),i(H),x&&Array.isArray(x)&&x.length>0&&x.forEach($=>{const D=H.findIndex(ie=>ie.day===$.day.toLowerCase());D!==-1&&(H[D].timeslots=$.timeslots)}),i(H),F(x||[]),t}catch(t){ee(S,t.code),C(u,t.message,3e3,"error")}finally{G(!1)}},V=async t=>{const T=M();h(!0);try{const x={};switch(l){case 0:x.bio=t.bio,t.photo&&(x.photo=t.photo);break;case 1:break;case 2:x.availability=T.availability,x.default_availability=T.availability;break;case 3:t&&t.skip_payment?x.not_paid_through_platform=1:x.not_paid_through_platform=0,x.completed=1;break}await W.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",x,"POST");const H=await J();L(H),l===3?(m(),_(!0)):k($=>$+1)}catch(x){console.error(x),C(u,x.message,3e3,"error"),ee(S,x.code)}finally{h(!1)}},O=()=>{k(Math.max(l-1,0))};n.useEffect(()=>{(async()=>{const t=await J();t&&k(B(t))})()},[]),n.useEffect(()=>{ne({title:p==null?void 0:p.name,path:"/coach/profile-setup",clubName:p==null?void 0:p.name,favicon:p==null?void 0:p.club_logo,description:"Coach Profile Setup"})},[p]);const le=()=>{const t=A();switch(l){case 0:return e.jsx(he,{onNext:V,register:P,setValue:j,errors:g,defaultValues:t,isSubmitting:d});case 1:return e.jsx(ge,{onNext:V,onBack:O,register:P,setValue:j,defaultValues:t,isSubmitting:d,clubSports:R,refetchCoachProfile:J});case 2:return e.jsx(fe,{onNext:V,onBack:O,selectedTimeSlot:r,originalAvailability:o,setSelectedTimeSlot:i,setOriginalAvailability:F,setValue:j,defaultValues:t,isSubmitting:d});case 3:return e.jsx(be,{onNext:V,onBack:O,isSubmitting:d,stripeConnectionData:U,setStripeConnectionData:s});default:return null}};return console.log(l),e.jsxs(pe,{children:[I&&e.jsx(Q,{}),e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("div",{className:"mb-10",children:l!==0&&e.jsxs("button",{className:"mt-5 flex items-center gap-2 text-[#525866]",onClick:O,children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{children:"Back"})]})})}),e.jsx("div",{className:"",children:le()}),y&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Sign up complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully signed up for Court Matchup. You can now access your coach portal"})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(Z,{onClick:()=>{m(),w("/coach/dashboard")},className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Coach portal!"})})]})})})]})]})}export{ls as default};
