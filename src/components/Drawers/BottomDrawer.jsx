import React, { useEffect } from "react";
import { InteractiveButton } from "Components/InteractiveButton";
import { IoClose } from "react-icons/io5";

const BottomDrawer = ({
  isOpen,
  onClose,
  title,
  children,
  onSave,
  onDiscard,
  onSecondaryAction,
  saveLabel = "Save",
  discardLabel = "Discard",
  secondaryActionLabel,
  showActions = false,
  leftElement = null,
  isSubmitting = false,
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed left-0 top-0 z-[99999] h-screen w-full">
      {/* Backdrop */}
      <div
        className="absolute left-0 top-0 h-full w-full bg-gray-500/75 transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="absolute bottom-0 left-0 h-[95vh] w-full transform overflow-auto rounded-t-xl bg-[#F6F8FA] transition-transform duration-300 ease-out">
        <div className="flex-1  pb-6">
          <div className="sticky top-0 z-50 mb-4 flex items-center justify-between border-b bg-white px-4 py-5">
            <div className="flex items-center">{leftElement}</div>
            <h3 className="text-center text-lg font-semibold capitalize leading-6 text-gray-900">
              {title}
            </h3>
            <div className="flex items-center gap-2">
              {showActions && secondaryActionLabel && (
                <button
                  type="button"
                  className="text-sm text-gray-600 hover:text-gray-800"
                  onClick={onSecondaryAction}
                >
                  {secondaryActionLabel}
                </button>
              )}
              {showActions && (
                <div className="flex gap-2">
                  <button
                    type="button"
                    className="rounded-lg border px-3 py-2 text-sm text-gray-500 hover:text-gray-700"
                    onClick={onDiscard}
                  >
                    {discardLabel}
                  </button>
                  <InteractiveButton
                    loading={isSubmitting}
                    type="button"
                    className="rounded-lg bg-primaryBlue px-3 py-2 text-sm font-medium text-white"
                    onClick={onSave}
                  >
                    {saveLabel}
                  </InteractiveButton>
                </div>
              )}
              <button
                type="button"
                className="rounded-md bg-white p-1 text-gray-400 hover:text-gray-500"
                onClick={onClose}
              >
                <span className="sr-only">Close</span>
                <IoClose size={24} />
              </button>
            </div>
          </div>
          <div className="overflow-y-auto bg-[#F6F8FA] px-4 pb-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BottomDrawer;
